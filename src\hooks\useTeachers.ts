
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

export type Teacher = {
  id: string;
  name: string;
  email: string;
  department_id: string | null;
  role: string;
  department?: {
    name: string;
  } | null;
  courses?: string[];
};

export const useTeachers = () => {
  return useQuery({
    queryKey: ["teachers"],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from("employees")
          .select(`
            *,
            department:departments!employees_department_id_fkey(name),
            teaches(
              course:courses(title)
            )
          `)
          .eq("role", "teacher");

        if (error) throw error;

        return data.map((teacher: any) => ({
          ...teacher,
          courses: teacher.teaches?.map((teach: any) => teach.course.title) || []
        }));
      } catch (err) {
        console.error("Error fetching teachers:", err);
        toast({
          title: "Error fetching teachers",
          description: (err as Error).message || "Something went wrong",
          variant: "destructive",
        });
        return [];
      }
    }
  });
};
