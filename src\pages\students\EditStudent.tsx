
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { Student } from "@/hooks/useStudents";

export default function EditStudent() {
  const { studentId } = useParams<{ studentId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  
  const form = useForm<Student>({
    defaultValues: {
      name: "",
      email: "",
      contact: "",
      address: "",
      dob: "",
    },
  });

  useEffect(() => {
    const fetchStudent = async () => {
      if (!studentId) {
        navigate('/students');
        return;
      }

      try {
        setIsLoading(true);
        const { data: student, error } = await supabase
          .from("students")
          .select("*")
          .eq("id", studentId)
          .single();

        if (error) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to fetch student details",
          });
          navigate('/students');
          return;
        }

        if (student) {
          form.reset(student);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchStudent();
  }, [studentId, form, toast, navigate]);

  const onSubmit = async (data: Student) => {
    if (!studentId) return;

    try {
      const { error } = await supabase
        .from("students")
        .update({
          name: data.name,
          email: data.email,
          contact: data.contact,
          address: data.address,
          dob: data.dob,
        })
        .eq("id", studentId);

      if (error) {
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to update student",
        });
        return;
      }

      toast({
        title: "Success",
        description: "Student updated successfully",
      });
      
      navigate(`/students/${studentId}`);
    } catch (error) {
      console.error("Error updating student:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred",
      });
    }
  };

  const handleCancel = () => {
    navigate(`/students/${studentId}`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Loading student data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => navigate(`/students/${studentId}`)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Student Details
        </Button>
      </div>
      
      <Card className="max-w-2xl mx-auto">
        <div className="p-6">
          <h2 className="text-2xl font-semibold mb-6">Edit Student</h2>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="contact"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="dob"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date of Birth</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-4 justify-end">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </div>
            </form>
          </Form>
        </div>
      </Card>
    </div>
  );
}
