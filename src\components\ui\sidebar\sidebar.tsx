
import * as React from "react"
import { cn } from "@/lib/utils"
import { useSidebar } from "./sidebar-context"

export interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  collapsed?: boolean
}

export const Sidebar = React.forwardRef<HTMLDivElement, SidebarProps>(
  ({ className, collapsed, children, ...props }, ref) => {
    const { collapsed: isCollapsed } = useSidebar()

    return (
      <div
        ref={ref}
        className={cn(
          "h-screen bg-background border-r z-30",
          isCollapsed ? "w-16" : "w-64",
          className
        )}
        data-state={isCollapsed ? "collapsed" : "expanded"}
        {...props}
      >
        {children}
      </div>
    )
  }
)
Sidebar.displayName = "Sidebar"

export const SidebarContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("flex flex-col h-full", className)}
      {...props}
    />
  )
})
SidebarContent.displayName = "SidebarContent"
