
import { useState, useCallback } from 'react';
import { toast } from "@/hooks/use-toast";
import { StudentDetails } from "@/types/student";
import { validatePaymentStatus } from "@/utils/paymentValidation";
import { determinePaymentStatus } from "@/utils/paymentUtils";
import { 
  fetchStudentDetailsFromApi, 
  enrollStudentInCourseApi,
  updatePaymentStatusApi 
} from "@/services/studentService";

// Helper function to validate UUID format
const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

export const useStudentDetails = () => {
  const [studentDetails, setStudentDetails] = useState<StudentDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchStudentDetails = useCallback(async (studentId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // Validate UUID format before making the API call
      if (!isValidUUID(studentId)) {
        throw new Error(`Invalid student ID format: ${studentId}. Expected a valid UUID.`);
      }
      
      const details = await fetchStudentDetailsFromApi(studentId);
      setStudentDetails(details);
      return details;
    } catch (err) {
      const error = err as Error;
      console.error('Error fetching student details:', error);
      setError(error);
      toast({
        title: "Error",
        description: error.message.includes('Invalid student ID format') 
          ? "Invalid student ID. Please check the URL and try again."
          : "Failed to fetch student details",
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const enrollStudentInCourse = async (studentId: string, courseId: string) => {
    try {
      const data = await enrollStudentInCourseApi(studentId, courseId);
      
      if (!data) {
        toast({
          title: "Already Enrolled",
          description: "Student is already enrolled in this course"
        });
        return null;
      }

      await fetchStudentDetails(studentId);
      
      toast({
        title: "Enrollment Successful",
        description: "Student has been enrolled in the course"
      });

      return data;
    } catch (error) {
      console.error('Error enrolling student:', error);
      toast({
        title: "Error",
        description: "Failed to enroll student in course",
        variant: "destructive"
      });
      return null;
    }
  };

  const updatePaymentStatus = async (paymentId: string, status: 'pending' | 'paid' | 'overdue') => {
    try {
      const data = await updatePaymentStatusApi(paymentId, status);

      if (studentDetails) {
        const updatedPayments = studentDetails.payments.map(p => 
          p.id === paymentId ? { ...p, status: validatePaymentStatus(status) } : p
        );
        
        setStudentDetails({
          ...studentDetails,
          payments: updatedPayments,
          paymentStatus: determinePaymentStatus(updatedPayments)
        });
      }

      toast({
        title: "Payment Status Updated",
        description: `Payment status changed to ${status}`
      });

      return data;
    } catch (error) {
      console.error('Error updating payment status:', error);
      toast({
        title: "Error",
        description: "Failed to update payment status",
        variant: "destructive"
      });
      return null;
    }
  };

  return {
    studentDetails,
    loading,
    error,
    fetchStudentDetails,
    enrollStudentInCourse,
    updatePaymentStatus
  };
};
