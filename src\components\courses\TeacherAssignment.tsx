
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { UserPlus, UserX } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useTeachers } from "@/hooks/useTeachers";

interface TeacherAssignmentProps {
  courseId: string;
  currentTeacherId: string | null;
  currentTeacherName: string;
  onTeacherUpdated: () => void;
}

export function TeacherAssignment({
  courseId,
  currentTeacherId,
  currentTeacherName,
  onTeacherUpdated
}: TeacherAssignmentProps) {
  const { data: teachers = [], isLoading: teachersLoading } = useTeachers();
  const [teacherAssignLoading, setTeacherAssignLoading] = useState(false);
  const [selectedTeacherId, setSelectedTeacherId] = useState<string>("");

  const handleAssignTeacher = async () => {
    if (!courseId || !selectedTeacherId) return;
    
    console.log("Starting teacher assignment process...");
    console.log("Course ID:", courseId);
    console.log("Selected Teacher ID:", selectedTeacherId);
    console.log("Current Teacher ID:", currentTeacherId);
    
    setTeacherAssignLoading(true);

    try {
      // First, unassign current teacher if there is one
      if (currentTeacherId) {
        console.log("Unassigning current teacher...");
        const { error: deleteError } = await supabase
          .from("teaches")
          .delete()
          .eq("course_id", courseId)
          .eq("employee_id", currentTeacherId);
        
        if (deleteError) {
          console.error("Error unassigning current teacher:", deleteError);
          throw deleteError;
        }
        console.log("Current teacher unassigned successfully");
      }

      // Then assign the new teacher
      console.log("Assigning new teacher...");
      const { error: insertError } = await supabase
        .from("teaches")
        .insert([{ 
          course_id: courseId, 
          employee_id: selectedTeacherId 
        }]);
      
      if (insertError) {
        console.error("Error assigning new teacher:", insertError);
        throw insertError;
      }
      
      console.log("New teacher assigned successfully");
      
      toast({
        title: "Teacher assigned!",
        description: "Teacher has been assigned to this course",
      });
      
      setSelectedTeacherId("");
      onTeacherUpdated();
      
    } catch (error) {
      console.error("Teacher assignment failed:", error);
      toast({
        title: "Error",
        description: `Failed to assign teacher: ${(error as Error).message}`,
        variant: "destructive",
      });
    } finally {
      setTeacherAssignLoading(false);
    }
  };

  const handleUnassignTeacher = async () => {
    if (!courseId || !currentTeacherId) return;
    
    console.log("Starting teacher unassignment process...");
    console.log("Course ID:", courseId);
    console.log("Current Teacher ID:", currentTeacherId);
    
    setTeacherAssignLoading(true);

    try {
      const { error } = await supabase
        .from("teaches")
        .delete()
        .eq("course_id", courseId)
        .eq("employee_id", currentTeacherId);
      
      if (error) {
        console.error("Error unassigning teacher:", error);
        throw error;
      }
      
      console.log("Teacher unassigned successfully");
      
      toast({
        title: "Teacher unassigned",
        description: "Teacher has been unassigned from this course",
      });
      
      onTeacherUpdated();
      
    } catch (error) {
      console.error("Teacher unassignment failed:", error);
      toast({
        title: "Error",
        description: `Failed to unassign teacher: ${(error as Error).message}`,
        variant: "destructive",
      });
    } finally {
      setTeacherAssignLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Teacher Assignment
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col md:flex-row gap-4 md:items-center">
        <div className="flex-1 min-w-0">
          <div className="text-muted-foreground mb-1">Current Teacher:</div>
          <div className="font-medium">{currentTeacherName}</div>
        </div>
        <div className="flex items-center gap-2">
          {currentTeacherId && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleUnassignTeacher}
              disabled={teacherAssignLoading}
            >
              <UserX className="mr-2 h-4 w-4" />
              {teacherAssignLoading ? "Unassigning..." : "Unassign Teacher"}
            </Button>
          )}
          <Select
            value={selectedTeacherId}
            onValueChange={setSelectedTeacherId}
            disabled={teacherAssignLoading || teachersLoading}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Assign a teacher" />
            </SelectTrigger>
            <SelectContent>
              {teachers
                .filter((t) => t.id !== currentTeacherId)
                .map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id}>
                    {teacher.name} ({teacher.email})
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
          <Button
            variant="default"
            size="sm"
            onClick={handleAssignTeacher}
            disabled={
              teacherAssignLoading ||
              !selectedTeacherId ||
              teachersLoading
            }
          >
            <UserPlus className="mr-2 h-4 w-4" />
            {teacherAssignLoading ? "Assigning..." : "Assign"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
