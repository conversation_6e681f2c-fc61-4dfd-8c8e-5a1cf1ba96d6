
import { useState, useMemo } from "react";
import { useEmployees } from "@/hooks/useEmployees";
import { AddEmployeeDialog } from "@/components/employees/AddEmployeeDialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useNavigate } from "react-router-dom";
import { EmployeeSearchBar } from "@/components/employees/EmployeeSearchBar";

// Extracts unique property values for filter options.
function getUnique(items: any[], fn: (item: any) => string | undefined): string[] {
  const set = new Set<string>();
  items.forEach(item => {
    const v = fn(item);
    if (v) set.add(v);
  });
  return Array.from(set).sort();
}

// For array fields (courses taught)
function getUniqueFromNested(items: any[], fn: (item: any) => string[] | undefined): string[] {
  const set = new Set<string>();
  items.forEach(item => {
    const arr = fn(item) || [];
    arr.forEach(v => v && set.add(v));
  });
  return Array.from(set).sort();
}

export default function Employees() {
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState<string[]>([]);
  const [departmentFilter, setDepartmentFilter] = useState<string[]>([]);
  const [teachingCourseFilter, setTeachingCourseFilter] = useState<string[]>([]);

  const { employees, isLoading, error, deleteEmployee } = useEmployees();
  const navigate = useNavigate();

  // Derive available filter options.
  const availableRoles = useMemo(
    () => getUnique(employees, emp => emp.role),
    [employees]
  );
  const availableDepartments = useMemo(
    () => getUnique(employees, emp => emp.department?.name),
    [employees]
  );
  const availableTeachingCourses = useMemo(
    () =>
      getUniqueFromNested(employees, emp =>
        Array.isArray(emp.teaches)
          ? emp.teaches
              .map((t: any) => t.course?.title)
              .filter(Boolean)
          : []
      ),
    [employees]
  );

  // Filtering logic, applied in memory for now (as with students)
  const filteredEmployees = useMemo(() => {
    return employees.filter(employee => {
      // Search
      const matchesSearch =
        searchQuery === "" ||
        employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        employee.role.toLowerCase().includes(searchQuery.toLowerCase());

      // Role filter
      const matchesRole =
        roleFilter.length === 0 || roleFilter.includes(employee.role);

      // Department filter
      const dept = employee.department?.name;
      const matchesDept =
        departmentFilter.length === 0 ||
        (dept && departmentFilter.includes(dept));

      // Teaching Course filter
      const teachingTitles =
        Array.isArray(employee.teaches)
          ? employee.teaches
              .map((t: any) => t.course?.title)
              .filter(Boolean)
          : [];
      const matchesTeaching =
        teachingCourseFilter.length === 0 ||
        teachingCourseFilter.some(c => teachingTitles.includes(c));

      return matchesSearch && matchesRole && matchesDept && matchesTeaching;
    });
  }, [employees, searchQuery, roleFilter, departmentFilter, teachingCourseFilter]);

  const handleDeleteEmployee = async (employeeId: string) => {
    if (confirm("Are you sure you want to delete this employee?")) {
      await deleteEmployee(employeeId);
    }
  };

  if (error) {
    console.error("Error loading employees:", error);
    // You can show a toast or UI error message if desired
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Employees</h1>
          <p className="text-muted-foreground">
            Manage your teaching staff and administrative employees
          </p>
        </div>
        <AddEmployeeDialog />
      </div>

      <EmployeeSearchBar
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        roleFilter={roleFilter}
        onRoleFilterChange={setRoleFilter}
        departmentFilter={departmentFilter}
        onDepartmentFilterChange={setDepartmentFilter}
        teachingCourseFilter={teachingCourseFilter}
        onTeachingCourseFilterChange={setTeachingCourseFilter}
        availableRoles={availableRoles}
        availableDepartments={availableDepartments}
        availableTeachingCourses={availableTeachingCourses}
      />

      {!isLoading && employees.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p>No employees found in the database.</p>
          <p className="text-sm">Add some employees to see them here.</p>
        </div>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader className="bg-muted/50">
            <TableRow className="hover:bg-transparent">
              <TableHead className="font-semibold">Name</TableHead>
              <TableHead className="font-semibold">Role</TableHead>
              <TableHead className="font-semibold">Department</TableHead>
              <TableHead className="font-semibold">Email</TableHead>
              <TableHead className="font-semibold">Courses Teaching</TableHead>
              <TableHead className="w-14 font-semibold"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                  Loading employees...
                </TableCell>
              </TableRow>
            ) : filteredEmployees.length > 0 ? (
              filteredEmployees.map((employee) => (
                <TableRow
                  key={employee.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => navigate(`/employees/${employee.id}`)}
                >
                  <TableCell className="font-medium">{employee.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{employee.role}</Badge>
                  </TableCell>
                  <TableCell>{employee.department?.name || 'No Department'}</TableCell>
                  <TableCell>{employee.email}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {employee.teaches && employee.teaches.length > 0 ? (
                        employee.teaches.map((teaching: any, index: number) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {teaching.course?.title || 'Unknown Course'}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-muted-foreground text-sm">No courses assigned</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/employees/${employee.id}`);
                          }}
                        >
                          View details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/employees/${employee.id}/edit`);
                          }}
                        >
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/employees/${employee.id}/courses`);
                          }}
                        >
                          View courses
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteEmployee(employee.id);
                          }}
                          className="text-destructive"
                        >
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                  No employees found matching your search criteria.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
