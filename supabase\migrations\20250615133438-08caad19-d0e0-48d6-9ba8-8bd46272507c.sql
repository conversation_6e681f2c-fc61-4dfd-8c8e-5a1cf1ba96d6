
-- First, let's check if the tables have any data
SELECT 'students' as table_name, COUNT(*) as count FROM students
UNION ALL
SELECT 'courses' as table_name, COUNT(*) as count FROM courses
UNION ALL
SELECT 'departments' as table_name, COUNT(*) as count FROM departments
UNION ALL
SELECT 'employees' as table_name, COUNT(*) as count FROM employees
UNION ALL
SELECT 'enrollments' as table_name, COUNT(*) as count FROM enrollments
UNION ALL
SELECT 'payments' as table_name, COUNT(*) as count FROM payments;

-- If the tables are empty, let's insert the sample data again
-- This time with a more direct approach

-- Insert departments first
INSERT INTO departments (name) VALUES 
('Computer Science'),
('Mathematics'),
('Physics'),
('Business Administration'),
('English Literature')
ON CONFLICT DO NOTHING;

-- Insert employees
INSERT INTO employees (name, email, role, department_id, salary) VALUES 
('<PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Computer Science' LIMIT 1), 75000),
('<PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Mathematics' LIMIT 1), 72000),
('Robert Davis', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Physics' LIMIT 1), 74000)
ON CONFLICT (email) DO NOTHING;

-- Insert students
INSERT INTO students (name, email, dob, address, contact) VALUES 
('Alex Thompson', '<EMAIL>', '2000-05-12', '123 Main St, Anytown, CA', '************'),
('Emma Rodriguez', '<EMAIL>', '2001-02-23', '456 Oak Ave, Somewhere, NY', '************'),
('Ryan Jackson', '<EMAIL>', '1999-11-08', '789 Pine Rd, Nowhere, TX', '************'),
('Olivia Kim', '<EMAIL>', '2002-07-15', '101 Maple Dr, Anywhere, FL', '************'),
('Ethan Patel', '<EMAIL>', '2000-09-30', '202 Cedar Ln, Everywhere, WA', '************')
ON CONFLICT (email) DO NOTHING;

-- Insert courses
INSERT INTO courses (title, description, duration_months, start_date, end_date, department_id, active) VALUES 
('Introduction to Programming', 'Learn the basics of programming with Python', 3, '2024-05-01', '2024-08-01', (SELECT id FROM departments WHERE name = 'Computer Science' LIMIT 1), true),
('Data Structures and Algorithms', 'Advanced programming concepts', 4, '2024-06-01', '2024-10-01', (SELECT id FROM departments WHERE name = 'Computer Science' LIMIT 1), true),
('Calculus I', 'Introduction to differential calculus', 4, '2024-05-01', '2024-09-01', (SELECT id FROM departments WHERE name = 'Mathematics' LIMIT 1), true)
ON CONFLICT DO NOTHING;

-- Insert enrollments (enroll each student in 2-3 courses)
INSERT INTO enrollments (student_id, course_id) 
SELECT s.id, c.id 
FROM students s 
CROSS JOIN courses c 
WHERE (s.name = 'Alex Thompson' AND c.title IN ('Introduction to Programming', 'Data Structures and Algorithms'))
   OR (s.name = 'Emma Rodriguez' AND c.title IN ('Introduction to Programming', 'Calculus I'))
   OR (s.name = 'Ryan Jackson' AND c.title IN ('Data Structures and Algorithms', 'Calculus I'))
   OR (s.name = 'Olivia Kim' AND c.title IN ('Introduction to Programming', 'Calculus I'))
   OR (s.name = 'Ethan Patel' AND c.title IN ('Data Structures and Algorithms', 'Introduction to Programming'))
ON CONFLICT DO NOTHING;

-- Insert sample payments
INSERT INTO payments (student_id, course_id, amount, method, status, paid_on)
SELECT 
    e.student_id,
    e.course_id,
    CASE 
        WHEN RANDOM() < 0.5 THEN 800
        ELSE 1200
    END,
    'credit_card',
    CASE 
        WHEN RANDOM() < 0.6 THEN 'paid'
        WHEN RANDOM() < 0.8 THEN 'partial'
        ELSE 'pending'
    END,
    CASE 
        WHEN RANDOM() < 0.6 THEN CURRENT_TIMESTAMP - (FLOOR(RANDOM() * 30) || ' days')::INTERVAL
        ELSE NULL
    END
FROM enrollments e
ON CONFLICT DO NOTHING;

-- Final verification query
SELECT 'Final Count Check' as status;
SELECT 'students' as table_name, COUNT(*) as count FROM students
UNION ALL
SELECT 'courses' as table_name, COUNT(*) as count FROM courses
UNION ALL
SELECT 'enrollments' as table_name, COUNT(*) as count FROM enrollments
UNION ALL
SELECT 'payments' as table_name, COUNT(*) as count FROM payments;
