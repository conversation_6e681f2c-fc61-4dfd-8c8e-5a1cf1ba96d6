
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const SUPABASE_URL = "https://hauhbpkmckufppgsityk.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "";
const GEMINI_API_KEY = Deno.env.get("GEMINI_API_KEY") ?? "";

// Safe SQL templates library
const SQL_TEMPLATES = [
  {
    intent: "student_list",
    description: "List all students",
    sql: "SELECT id, name, email, address FROM students ORDER BY name LIMIT 100"
  },
  {
    intent: "course_list",
    description: "List all courses",
    sql: "SELECT id, title, description, duration_months, start_date, end_date, active FROM courses ORDER BY title LIMIT 100"
  },
  {
    intent: "students_by_course",
    description: "Find students enrolled in a specific course",
    sql: "SELECT s.name, s.email, c.title FROM students s JOIN enrollments e ON s.id = e.student_id JOIN courses c ON e.course_id = c.id WHERE LOWER(c.title) LIKE LOWER('%{course_name}%') ORDER BY s.name"
  },
  {
    intent: "courses_by_department",
    description: "Find courses offered by a specific department",
    sql: "SELECT c.title, c.description, d.name as department FROM courses c JOIN departments d ON c.department_id = d.id WHERE LOWER(d.name) LIKE LOWER('%{department_name}%') ORDER BY c.title"
  },
  {
    intent: "employee_by_role",
    description: "Find employees by role",
    sql: "SELECT name, email, role, salary FROM employees WHERE LOWER(role) = LOWER('{role}') ORDER BY name"
  },
  {
    intent: "payment_status",
    description: "Check payment status",
    sql: "SELECT s.name, c.title, p.amount, p.status, p.paid_on FROM payments p JOIN students s ON p.student_id = s.id JOIN courses c ON p.course_id = c.id WHERE p.status = '{status}' ORDER BY p.paid_on DESC"
  },
  {
    intent: "enrollment_count_by_course",
    description: "Get enrollment count by course",
    sql: "SELECT c.title, COUNT(e.id) as enrollment_count FROM courses c LEFT JOIN enrollments e ON c.id = e.course_id GROUP BY c.id, c.title ORDER BY enrollment_count DESC"
  },
  {
    intent: "teacher_course_assignment",
    description: "Find courses assigned to teachers",
    sql: "SELECT e.name as teacher_name, c.title FROM employees e JOIN teacher_course_map tcm ON e.id = tcm.employee_id JOIN courses c ON tcm.course_id = c.id WHERE e.role = 'teacher' ORDER BY e.name, c.title"
  },
  {
    intent: "student_payment_summary",
    description: "Get payment summary for students",
    sql: "SELECT s.name, SUM(p.amount) as total_paid FROM students s JOIN payments p ON s.id = p.student_id GROUP BY s.id, s.name ORDER BY total_paid DESC"
  },
  {
    intent: "course_duration_stats",
    description: "Get statistics on course durations",
    sql: "SELECT duration_months, COUNT(*) as course_count FROM courses GROUP BY duration_months ORDER BY duration_months"
  },
  {
    intent: "payment_method_distribution",
    description: "Get distribution of payment methods",
    sql: "SELECT method, COUNT(*) as payment_count, SUM(amount) as total_amount FROM payments GROUP BY method ORDER BY total_amount DESC"
  },
  {
    intent: "payment_status_distribution",
    description: "Get distribution of payment statuses",
    sql: "SELECT status, COUNT(*) as payment_count, SUM(amount) as total_amount FROM payments GROUP BY status ORDER BY total_amount DESC"
  },
  {
    intent: "teacher_student_ratio",
    description: "Calculate teacher to student ratio",
    sql: "WITH teacher_count AS (SELECT COUNT(*) as count FROM employees WHERE role = 'teacher'), student_count AS (SELECT COUNT(*) as count FROM students) SELECT teacher_count.count as teachers, student_count.count as students, ROUND(student_count.count::numeric / NULLIF(teacher_count.count, 0), 2) as ratio FROM teacher_count, student_count"
  },
  {
    intent: "top_departments_by_courses",
    description: "Find departments with the most courses",
    sql: "SELECT d.name, COUNT(c.id) as course_count FROM departments d LEFT JOIN courses c ON d.id = c.department_id GROUP BY d.id, d.name ORDER BY course_count DESC"
  }
];

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

async function matchQueryToTemplate(query: string) {
  try {
    const prompt = `
    You are an AI assistant that matches user queries to SQL templates. 
    Here's the user query: "${query}"
    
    Based on this query, select the most appropriate template from this list:
    ${JSON.stringify(SQL_TEMPLATES.map(t => ({ intent: t.intent, description: t.description })))}
    
    Also identify any parameters that need to be extracted from the query to fill in the SQL template.
    
    Respond with a JSON object like this:
    {
      "intent": "the_matched_intent",
      "parameters": {
        "param_name1": "extracted_value1",
        "param_name2": "extracted_value2"
      }
    }
    
    Only respond with the JSON object, nothing else.
    `;

    const response = await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.0-pro:generateContent?key=" + GEMINI_API_KEY, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0,
          topP: 0.1,
          topK: 16
        }
      })
    });

    const data = await response.json();
    const content = data.candidates[0].content.parts[0].text;
    
    // Clean up the JSON string if necessary
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    const jsonStr = jsonMatch ? jsonMatch[0] : content;
    
    return JSON.parse(jsonStr);
  } catch (error) {
    console.error("Error matching query to template:", error);
    throw error;
  }
}

async function executeQuery(sql: string) {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
  const { data, error } = await supabase.rpc('execute_read_only_query', {
    query_text: sql
  });
  
  if (error) throw error;
  return data;
}

async function processQuery(query: string) {
  // Match the query to a template
  const match = await matchQueryToTemplate(query);
  
  // Find the template
  const template = SQL_TEMPLATES.find(t => t.intent === match.intent);
  
  if (!template) {
    throw new Error("No matching template found for query");
  }
  
  // Fill in the parameters
  let sql = template.sql;
  if (match.parameters) {
    Object.entries(match.parameters).forEach(([key, value]) => {
      sql = sql.replace(`{${key}}`, value as string);
    });
  }
  
  console.log("Executing SQL:", sql);
  
  // Execute the query
  const results = await executeQuery(sql);
  
  // Determine if the result is suitable for visualization
  let chartType = null;
  let chartData = null;
  
  // If we have results with numerical data, prepare chart data
  if (results && results.length > 0) {
    const firstRow = results[0];
    const keys = Object.keys(firstRow);
    
    // Identify potential numerical columns for charts
    const numericalColumns = keys.filter(key => {
      const val = firstRow[key];
      return typeof val === 'number' || (typeof val === 'string' && !isNaN(Number(val)));
    });
    
    // Identify potential label columns for charts
    const labelColumns = keys.filter(key => typeof firstRow[key] === 'string' && !numericalColumns.includes(key));
    
    if (numericalColumns.length > 0 && labelColumns.length > 0) {
      const labelColumn = labelColumns[0];
      const dataColumn = numericalColumns[0];
      
      // Determine chart type based on data structure
      if (results.length <= 8) {
        chartType = 'pie';
      } else if (results.length <= 30) {
        chartType = 'bar';
      } else {
        chartType = 'line';
      }
      
      chartData = {
        labels: results.map(row => row[labelColumn]),
        datasets: [{
          label: dataColumn,
          data: results.map(row => Number(row[dataColumn]))
        }]
      };
    }
  }
  
  return {
    sql,
    results,
    chartType,
    chartData
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  
  try {
    const { query } = await req.json();
    
    if (!query) {
      return new Response(
        JSON.stringify({ error: "Query is required" }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
    
    const result = await processQuery(query);
    
    return new Response(
      JSON.stringify(result),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  } catch (error) {
    console.error("Error processing request:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
