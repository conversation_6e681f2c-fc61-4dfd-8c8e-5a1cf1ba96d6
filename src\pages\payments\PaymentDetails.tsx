
import { useParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { usePayments, Payment } from "@/hooks/usePayments";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { PaymentDetailsHeader } from "@/components/payments/PaymentDetailsHeader";
import { PaymentInformationCard } from "@/components/payments/PaymentInformationCard";
import { PaymentRelatedInfoCard } from "@/components/payments/PaymentRelatedInfoCard";
import { PaymentDetailsLoading } from "@/components/payments/PaymentDetailsLoading";
import { PaymentNotFound } from "@/components/payments/PaymentNotFound";

// Helper function to validate UUID format
const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

export default function PaymentDetails() {
  const { paymentId } = useParams<{ paymentId: string }>();
  const { updatePayment } = usePayments();
  const [loading, setLoading] = useState(true);
  const [payment, setPayment] = useState<Payment | null>(null);

  useEffect(() => {
    const loadPaymentDetails = async () => {
      if (!paymentId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        
        // Validate UUID format before making the API call
        if (!isValidUUID(paymentId)) {
          console.log('Invalid payment ID format:', paymentId);
          toast({
            title: "Error",
            description: "Invalid payment ID. Please check the URL and try again.",
            variant: "destructive",
          });
          setPayment(null);
          setLoading(false);
          return;
        }
        
        console.log("Fetching payment details for ID:", paymentId);
        
        const { data, error } = await supabase
          .from('payments')
          .select(`
            *,
            student:students(name, email),
            course:courses(title)
          `)
          .eq('id', paymentId)
          .maybeSingle();

        if (error) {
          console.error('Error fetching payment:', error);
          toast({
            title: "Error",
            description: "Failed to fetch payment details",
            variant: "destructive",
          });
          return;
        }

        if (!data) {
          console.log('Payment not found for ID:', paymentId);
          setPayment(null);
          return;
        }

        console.log('Payment details loaded:', data);
        setPayment(data as Payment);
      } catch (err) {
        console.error('Error loading payment details:', err);
        toast({
          title: "Error",
          description: "Failed to load payment details",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadPaymentDetails();
  }, [paymentId]);

  const handleStatusChange = async (newStatus: 'pending' | 'paid' | 'overdue' | 'partial') => {
    if (!payment) return;
    
    const updatedPayment = await updatePayment(payment.id, { status: newStatus });
    
    if (updatedPayment) {
      setPayment(prev => prev ? { ...prev, status: newStatus } : null);
    }
  };

  if (loading) {
    return <PaymentDetailsLoading />;
  }

  if (!payment || !paymentId) {
    return <PaymentNotFound paymentId={paymentId || ''} />;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PaymentDetailsHeader paymentId={paymentId} />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <PaymentInformationCard payment={payment} />
        <PaymentRelatedInfoCard 
          payment={payment} 
          onStatusChange={handleStatusChange} 
        />
      </div>
    </div>
  );
}
