import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Download, Printer } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Payment } from "@/hooks/usePayments";
import { toast } from "@/hooks/use-toast";

// Helper function to validate UUID format
const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

export default function PaymentReceipt() {
  const { paymentId } = useParams<{ paymentId: string }>();
  const navigate = useNavigate();
  const [payment, setPayment] = useState<Payment | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadPaymentDetails = async () => {
      if (!paymentId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        
        if (!isValidUUID(paymentId)) {
          toast({
            title: "Error",
            description: "Invalid payment ID format.",
            variant: "destructive",
          });
          setPayment(null);
          setLoading(false);
          return;
        }
        
        const { data, error } = await supabase
          .from('payments')
          .select(`
            *,
            student:students(name, email, address, contact),
            course:courses(title, description)
          `)
          .eq('id', paymentId)
          .maybeSingle();

        if (error) {
          console.error('Error fetching payment:', error);
          toast({
            title: "Error",
            description: "Failed to fetch payment details",
            variant: "destructive",
          });
          return;
        }

        if (!data) {
          setPayment(null);
          return;
        }

        setPayment(data as Payment);
      } catch (err) {
        console.error('Error loading payment details:', err);
        toast({
          title: "Error",
          description: "Failed to load payment details",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadPaymentDetails();
  }, [paymentId]);

  const downloadReceipt = () => {
    if (!payment) return;

    const receiptContent = generateReceiptContent(payment);
    const blob = new Blob([receiptContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `payment-receipt-${payment.id}.txt`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  const printReceipt = () => {
    window.print();
  };

  const generateReceiptContent = (payment: Payment): string => {
    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    return `
PAYMENT RECEIPT
=====================================

Receipt ID: ${payment.id}
Date Issued: ${formatDate(new Date().toISOString())}

STUDENT INFORMATION
-------------------------------------
Name: ${payment.student?.name || 'N/A'}
Email: ${payment.student?.email || 'N/A'}
${payment.student?.address ? `Address: ${payment.student.address}` : ''}
${payment.student?.contact ? `Contact: ${payment.student.contact}` : ''}

COURSE INFORMATION
-------------------------------------
Course: ${payment.course?.title || 'General Payment'}
${payment.course?.description ? `Description: ${payment.course.description}` : ''}

PAYMENT DETAILS
-------------------------------------
Amount: $${payment.amount.toFixed(2)}
Payment Method: ${payment.method.replace('_', ' ').toUpperCase()}
Status: ${payment.status.toUpperCase()}
Transaction Reference: ${payment.transaction_ref || 'N/A'}
Payment Date: ${payment.paid_on ? formatDate(payment.paid_on) : 'Not yet paid'}
Record Created: ${formatDate(payment.created_at)}

=====================================
Thank you for your payment!

This is an official receipt for your records.
Please keep this receipt for your files.
    `.trim();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <p>Loading payment receipt...</p>
        </div>
      </div>
    );
  }

  if (!payment) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Payment Not Found</h2>
          <p className="text-muted-foreground mb-6">
            The payment receipt you're looking for could not be found.
          </p>
          <Button onClick={() => navigate('/payments')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Payments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between no-print">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => navigate('/payments')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Payments
          </Button>
          <h1 className="text-2xl font-bold">Payment Receipt</h1>
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={downloadReceipt}
          >
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={printReceipt}
          >
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      <div className="max-w-4xl mx-auto print:max-w-none">
        <Card className="print:shadow-none print:border-none">
          <CardHeader className="text-center border-b">
            <CardTitle className="text-3xl">Payment Receipt</CardTitle>
            <p className="text-muted-foreground">Receipt ID: {payment.id}</p>
            <p className="text-sm text-muted-foreground">
              Issued on: {formatDate(new Date().toISOString())}
            </p>
          </CardHeader>
          
          <CardContent className="space-y-8 p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4 border-b pb-2">Student Information</h3>
                <div className="space-y-2">
                  <p><span className="font-medium">Name:</span> {payment.student?.name || 'N/A'}</p>
                  <p><span className="font-medium">Email:</span> {payment.student?.email || 'N/A'}</p>
                  {payment.student?.address && (
                    <p><span className="font-medium">Address:</span> {payment.student.address}</p>
                  )}
                  {payment.student?.contact && (
                    <p><span className="font-medium">Contact:</span> {payment.student.contact}</p>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4 border-b pb-2">Course Information</h3>
                <div className="space-y-2">
                  <p><span className="font-medium">Course:</span> {payment.course?.title || 'General Payment'}</p>
                  {payment.course?.description && (
                    <p><span className="font-medium">Description:</span> {payment.course.description}</p>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 border-b pb-2">Payment Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <p><span className="font-medium">Amount:</span> <span className="text-2xl font-bold">${payment.amount.toFixed(2)}</span></p>
                  <p><span className="font-medium">Payment Method:</span> {payment.method.replace('_', ' ').toUpperCase()}</p>
                  <p><span className="font-medium">Status:</span> 
                    <span className={`ml-2 px-2 py-1 rounded text-sm ${
                      payment.status === 'paid' ? 'bg-green-100 text-green-800' :
                      payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      payment.status === 'overdue' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {payment.status.toUpperCase()}
                    </span>
                  </p>
                </div>
                <div className="space-y-2">
                  <p><span className="font-medium">Transaction Reference:</span> {payment.transaction_ref || 'N/A'}</p>
                  <p><span className="font-medium">Payment Date:</span> {payment.paid_on ? formatDate(payment.paid_on) : 'Not yet paid'}</p>
                  <p><span className="font-medium">Record Created:</span> {formatDate(payment.created_at)}</p>
                </div>
              </div>
            </div>

            <div className="text-center border-t pt-6 text-muted-foreground">
              <p className="font-medium">Thank you for your payment!</p>
              <p className="text-sm">This is an official receipt for your records. Please keep this receipt for your files.</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <style>{`
        @media print {
          .no-print {
            display: none !important;
          }
          body {
            margin: 0;
            padding: 0;
          }
        }
      `}</style>
    </div>
  );
}
