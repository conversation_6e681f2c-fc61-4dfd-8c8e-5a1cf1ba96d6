
import { useState } from 'react';
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Payment, PaymentFilters, CreatePaymentData, UpdatePaymentData } from "@/types/payment";
import { transformPayment } from "@/utils/paymentValidation";

export const usePaymentsCore = () => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchPayments = async (filters?: PaymentFilters) => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('payments')
        .select(`
          *,
          student:students(name, email),
          course:courses(title)
        `)
        .order('created_at', { ascending: false });
      
      if (filters?.studentId) {
        query = query.eq('student_id', filters.studentId);
      }
      
      if (filters?.courseId) {
        query = query.eq('course_id', filters.courseId);
      }

      const { data, error } = await query;

      if (error) throw error;

      const typedPayments = data.map(transformPayment);
      setPayments(typedPayments);
      return typedPayments;
    } catch (err) {
      console.error('Error fetching payments:', err);
      toast({
        title: "Error fetching payments",
        description: (err as Error).message || "Something went wrong",
        variant: "destructive",
      });
      return [];
    } finally {
      setLoading(false);
    }
  };

  const createPayment = async (paymentData: CreatePaymentData) => {
    try {
      const { data, error } = await supabase
        .from('payments')
        .insert(paymentData)
        .select(`
          *,
          student:students(name, email),
          course:courses(title)
        `)
        .single();

      if (error) throw error;

      const typedPayment = transformPayment(data);
      setPayments(prev => [typedPayment, ...prev]);
      
      toast({
        title: "Payment recorded",
        description: "The payment has been successfully recorded",
      });
      
      return typedPayment;
    } catch (err) {
      console.error('Error creating payment:', err);
      toast({
        title: "Error recording payment",
        description: (err as Error).message || "Something went wrong",
        variant: "destructive",
      });
      return null;
    }
  };

  const updatePayment = async (id: string, paymentData: UpdatePaymentData) => {
    try {
      const { data, error } = await supabase
        .from('payments')
        .update(paymentData)
        .eq('id', id)
        .select(`
          *,
          student:students(name, email),
          course:courses(title)
        `)
        .single();

      if (error) throw error;

      const typedPayment = transformPayment(data);
      setPayments(prev => prev.map(p => p.id === id ? typedPayment : p));
      
      toast({
        title: "Payment updated",
        description: "The payment has been successfully updated",
      });
      
      return typedPayment;
    } catch (err) {
      console.error('Error updating payment:', err);
      toast({
        title: "Error updating payment",
        description: (err as Error).message || "Something went wrong",
        variant: "destructive",
      });
      return null;
    }
  };

  const deletePayment = async (id: string) => {
    try {
      const { error } = await supabase
        .from('payments')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setPayments(prev => prev.filter(p => p.id !== id));
      
      toast({
        title: "Payment deleted",
        description: "The payment has been successfully deleted",
      });
      
      return true;
    } catch (err) {
      console.error('Error deleting payment:', err);
      toast({
        title: "Error deleting payment",
        description: (err as Error).message || "Something went wrong",
        variant: "destructive",
      });
      return false;
    }
  };

  const getStudentBalance = async (studentId: string, courseId?: string) => {
    try {
      // Calculate balance by getting all payments for the student/course
      let paymentsQuery = supabase
        .from('payments')
        .select('amount, course_id')
        .eq('student_id', studentId)
        .eq('status', 'completed');
        
      if (courseId) {
        paymentsQuery = paymentsQuery.eq('course_id', courseId);
      }
      
      const { data: paymentsData, error: paymentsError } = await paymentsQuery;

      if (paymentsError) throw paymentsError;
      
      // For now, return a simple structure showing total paid
      const totalPaid = paymentsData?.reduce((sum, payment) => sum + Number(payment.amount), 0) || 0;
      
      return [{
        student_id: studentId,
        course_id: courseId || null,
        total_paid: totalPaid,
        balance: 0 // This would need to be calculated based on course fees
      }];
    } catch (err) {
      console.error('Error fetching balance:', err);
      toast({
        title: "Error fetching balance",
        description: (err as Error).message || "Something went wrong",
        variant: "destructive",
      });
      return [];
    }
  };

  return {
    payments,
    loading,
    fetchPayments,
    createPayment,
    updatePayment,
    deletePayment,
    getStudentBalance
  };
};
