
import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Calendar, Clock } from "lucide-react";
import { format } from "date-fns";
import { CourseDetails } from "@/hooks/useCourseDetails";

interface CourseInfoProps {
  courseDetails: CourseDetails;
}

export function CourseInfo({ courseDetails }: CourseInfoProps) {
  const getStatusColor = (active: boolean | null) => {
    if (active === true) return "bg-success/20 text-success hover:bg-success/30";
    if (active === false) return "bg-destructive/20 text-destructive hover:bg-destructive/30";
    return "bg-muted text-muted-foreground";
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-2xl font-bold">
          {courseDetails.title}
          <Badge className={`ml-2 ${getStatusColor(courseDetails.active)}`}>
            {courseDetails.active ? 'Active' : courseDetails.active === false ? 'Inactive' : 'Draft'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="grid gap-6">
        <div>
          <p className="text-muted-foreground mb-2">Description</p>
          <p>{courseDetails.description || 'No description available'}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">Department</p>
            <p>{courseDetails.department?.name || 'Not assigned'}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">Instructor</p>
            <p>{courseDetails.instructor || 'Unassigned'}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">Students Enrolled</p>
            <p>{courseDetails.enrollment_count}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Duration</p>
              <p>{courseDetails.duration_months} months</p>
            </div>
          </div>

          {courseDetails.start_date && (
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Start Date</p>
                <p>{format(new Date(courseDetails.start_date), 'MMM dd, yyyy')}</p>
              </div>
            </div>
          )}

          {courseDetails.end_date && (
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">End Date</p>
                <p>{format(new Date(courseDetails.end_date), 'MMM dd, yyyy')}</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
