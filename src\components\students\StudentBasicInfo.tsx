
import { StudentDetails } from "@/types/student";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Info } from "lucide-react";
import { getPaymentStatusColor } from "@/utils/statusColorUtils";

interface StudentBasicInfoProps {
  student: StudentDetails;
}

export const StudentBasicInfo = ({ student }: StudentBasicInfoProps) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-3xl font-bold">{student.name}</CardTitle>
        <Badge 
          variant="outline" 
          className={getPaymentStatusColor(student.paymentStatus)}
        >
          {student.paymentStatus.toUpperCase()}
        </Badge>
      </CardHeader>
      <CardContent className="grid gap-4 md:grid-cols-2">
        <div>
          <h3 className="font-semibold flex items-center gap-2">
            <Info className="h-4 w-4" />
            Contact Information
          </h3>
          <div className="mt-2 space-y-2">
            <p><span className="text-muted-foreground">Email:</span> {student.email}</p>
            <p><span className="text-muted-foreground">Contact:</span> {student.contact || 'Not provided'}</p>
            <p><span className="text-muted-foreground">Address:</span> {student.address || 'Not provided'}</p>
            <p><span className="text-muted-foreground">DOB:</span> {student.dob ? new Date(student.dob).toLocaleDateString() : 'Not provided'}</p>
          </div>
        </div>
        <div>
          <h3 className="font-semibold">Academic Overview</h3>
          <div className="mt-2 space-y-2">
            <p><span className="text-muted-foreground">Courses Enrolled:</span> {student.courses.length}</p>
            <p><span className="text-muted-foreground">Registration Date:</span> {new Date(student.created_at).toLocaleDateString()}</p>
            <p><span className="text-muted-foreground">Total Payments:</span> ${student.payments.reduce((sum, payment) => sum + payment.amount, 0).toFixed(2)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
