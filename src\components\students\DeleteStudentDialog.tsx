
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { StudentWithPayments } from "@/hooks/useStudents";

interface DeleteStudentDialogProps {
  student: StudentWithPayments;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStudentDeleted: () => void;
}

export function DeleteStudentDialog({ 
  student, 
  open, 
  onOpenChange, 
  onStudentDeleted 
}: DeleteStudentDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();

  const handleDelete = async () => {
    setIsDeleting(true);
    
    try {
      console.log("Starting student deletion process for:", student.name);
      
      // Delete related enrollments first
      const { error: enrollmentsError } = await supabase
        .from("enrollments")
        .delete()
        .eq("student_id", student.id);
      
      if (enrollmentsError) {
        console.error("Error deleting enrollments:", enrollmentsError);
        throw enrollmentsError;
      }
      
      console.log("Enrollments deleted successfully");
      
      // Delete related payments
      const { error: paymentsError } = await supabase
        .from("payments")
        .delete()
        .eq("student_id", student.id);
      
      if (paymentsError) {
        console.error("Error deleting payments:", paymentsError);
        throw paymentsError;
      }
      
      console.log("Payments deleted successfully");
      
      // Finally delete the student
      const { error: studentError } = await supabase
        .from("students")
        .delete()
        .eq("id", student.id);
      
      if (studentError) {
        console.error("Error deleting student:", studentError);
        throw studentError;
      }
      
      console.log("Student deleted successfully");
      
      toast({
        title: "Student deleted",
        description: `${student.name} has been successfully deleted.`,
      });
      
      onStudentDeleted();
      onOpenChange(false);
      
    } catch (error) {
      console.error("Failed to delete student:", error);
      toast({
        title: "Error",
        description: `Failed to delete student: ${(error as Error).message}`,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Student</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete <strong>{student.name}</strong>? 
            This action cannot be undone and will also remove all their enrollments and payment records.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? "Deleting..." : "Delete Student"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
