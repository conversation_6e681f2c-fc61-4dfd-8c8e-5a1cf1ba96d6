
-- Function to safely execute read-only queries for the LLM Query Assistant
CREATE OR REPLACE FUNCTION public.execute_read_only_query(query_text text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  query_plan jsonb;
  result jsonb;
BEGIN
  -- Check if query is read-only by examining the query plan
  EXECUTE 'EXPLAIN (FORMAT JSON) ' || query_text INTO query_plan;
  
  -- Extract the command type from the query plan
  IF query_plan->0->'Plan'->>'Command' NOT IN ('Select') THEN
    RAISE EXCEPTION 'Only SELECT queries are allowed';
  END IF;

  -- Execute the query and return the results
  EXECUTE 'SELECT jsonb_agg(row_to_json(t)) FROM (' || query_text || ') t' INTO result;
  
  -- Handle null result (no rows)
  IF result IS NULL THEN
    RETURN '[]'::jsonb;
  END IF;
  
  RETURN result;
EXCEPTION
  WHEN others THEN
    RAISE EXCEPTION 'Query execution error: %', SQLERRM;
END;
$$;

-- Set permissions so it can only be used by service role
REVOKE ALL ON FUNCTION public.execute_read_only_query(text) FROM public;
GRANT EXECUTE ON FUNCTION public.execute_read_only_query(text) TO service_role;
