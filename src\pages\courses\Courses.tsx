
import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useCourses } from "@/hooks/useCourses";
import { CourseSearchBar } from "@/components/courses/CourseSearchBar";
import { ViewToggle } from "@/components/courses/ViewToggle";
import { CourseCard } from "@/components/courses/CourseCard";
import { CourseTable } from "@/components/courses/CourseTable";
import { AddCourseDialog } from "@/components/courses/AddCourseDialog";

export default function Courses() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [departmentFilter, setDepartmentFilter] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");
  const navigate = useNavigate();
  const { data: courses = [], isLoading } = useCourses();
  
  // Get unique departments for filter options
  const availableDepartments = useMemo(() => {
    const departments = new Set<string>();
    courses.forEach(course => {
      if (course.department?.name) {
        departments.add(course.department.name);
      }
    });
    return Array.from(departments).sort();
  }, [courses]);

  const filteredCourses = useMemo(() => {
    return courses.filter(course => {
      // Search filter
      const matchesSearch = searchQuery === "" || 
        course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        course.instructor?.toLowerCase().includes(searchQuery.toLowerCase());

      // Status filter
      const courseStatus = course.active === true ? 'active' : 
                          course.active === false ? 'inactive' : 'draft';
      const matchesStatus = statusFilter.length === 0 ||
        statusFilter.includes(courseStatus);

      // Department filter
      const matchesDepartment = departmentFilter.length === 0 ||
        (course.department?.name && departmentFilter.includes(course.department.name));

      return matchesSearch && matchesStatus && matchesDepartment;
    });
  }, [courses, searchQuery, statusFilter, departmentFilter]);
  
  const getStatusColor = (active: boolean | null) => {
    if (active === true) return "bg-success/20 text-success hover:bg-success/30";
    if (active === false) return "bg-destructive/20 text-destructive hover:bg-destructive/30";
    return "bg-muted text-muted-foreground";
  };
  

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Courses</h1>
          <p className="text-muted-foreground">
            Manage all your course offerings and curriculum
          </p>
        </div>
        <AddCourseDialog />
      </div>
      
      <div className="flex items-center gap-4">
        <CourseSearchBar 
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          statusFilter={statusFilter}
          onStatusFilterChange={setStatusFilter}
          departmentFilter={departmentFilter}
          onDepartmentFilterChange={setDepartmentFilter}
          availableDepartments={availableDepartments}
        />
        <ViewToggle 
          viewMode={viewMode}
          onViewChange={setViewMode}
        />
      </div>
      
      {viewMode === "list" ? (
        <CourseTable 
          courses={filteredCourses}
          getStatusColor={getStatusColor}
          isLoading={isLoading}
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredCourses.length > 0 ? (
            filteredCourses.map((course) => (
              <CourseCard
                key={course.id}
                course={course}
                getStatusColor={getStatusColor}
              />
            ))
          ) : (
            <div className="col-span-full text-center py-12 text-muted-foreground">
              No courses found matching your search criteria.
            </div>
          )}
        </div>
      )}
    </div>
  );
}
