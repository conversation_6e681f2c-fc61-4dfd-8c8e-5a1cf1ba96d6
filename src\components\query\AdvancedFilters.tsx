
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, Search, Plus, Trash2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";
import { DatePicker } from "./DatePicker";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

type FilterCondition = {
  id: string;
  entity: string;
  field: string;
  operator: string;
  value: string;
};

// Field type mappings
const FIELD_TYPES = {
  students: {
    name: 'text',
    email: 'text', 
    dob: 'date',
    address: 'text',
    contact: 'text',
  },
  courses: {
    title: 'text',
    description: 'text',
    duration_months: 'number',
    start_date: 'date',
    end_date: 'date',
    active: 'boolean',
  },
  payments: {
    amount: 'number',
    method: 'text',
    status: 'text',
    paid_on: 'date',
  },
  enrollments: {
    enrolled_on: 'date',
  },
  employees: {
    name: 'text',
    email: 'text',
    role: 'text',
    salary: 'number',
    salary_paid: 'number',
  },
  departments: {
    name: 'text',
  }
} as const;

export function AdvancedFilters() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any[] | null>(null);
  const [showAllData, setShowAllData] = useState(false);
  const [selectedEntity, setSelectedEntity] = useState("students");
  const [conditions, setConditions] = useState<FilterCondition[]>([
    { id: "1", entity: "students", field: "name", operator: "contains", value: "" }
  ]);

  const addCondition = () => {
    const newId = (conditions.length + 1).toString();
    setConditions([...conditions, { id: newId, entity: selectedEntity, field: "name", operator: "contains", value: "" }]);
  };

  const removeCondition = (id: string) => {
    setConditions(conditions.filter(condition => condition.id !== id));
  };

  const updateCondition = (id: string, field: keyof FilterCondition, value: string) => {
    setConditions(
      conditions.map(condition => 
        condition.id === id ? { ...condition, [field]: value } : condition
      )
    );
  };

  const handleFilterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    setResults(null);
    
    try {
      // Handle "Show All" case
      if (showAllData) {
        const { data, error } = await supabase.functions.invoke('advanced-filter', {
          body: { getAllData: true, entity: selectedEntity }
        });
        
        if (error) throw error;
        setResults(data.results);
        toast({
          title: "All data retrieved",
          description: `Retrieved all records from ${selectedEntity}`,
        });
        setIsLoading(false);
        return;
      }
      
      // Filter out empty conditions and prepare for dynamic query
      const validConditions = conditions.filter(c => {
        // Include conditions that don't need values
        const noValueOperators = ['is_null', 'is_not_null', 'is_empty', 'is_not_empty', 'is_true', 'is_false'];
        return c.value.trim() !== "" || noValueOperators.includes(c.operator);
      });
      
      if (validConditions.length === 0) {
        toast({
          title: "No filters applied",
          description: "Please add at least one filter condition or select 'Show All Data'",
        });
        setIsLoading(false);
        return;
      }
      
      const { data, error } = await supabase.functions.invoke('advanced-filter', {
        body: { 
          entity: selectedEntity,
          conditions: validConditions.map(c => ({
            field: c.field,
            operator: c.operator,
            value: c.value
          }))
        }
      });
      
      if (error) throw error;
      
      setResults(data.results);
      toast({
        title: "Filters applied successfully",
        description: `Found ${data.results.length} results`,
      });
    } catch (err) {
      console.error('Error applying filters:', err);
      toast({
        variant: "destructive",
        title: "Filter error",
        description: (err as Error).message || "Failed to apply filters"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Field options based on entity
  const getFieldOptions = (entity: string) => {
    const entityFields = FIELD_TYPES[entity as keyof typeof FIELD_TYPES] || {};
    return Object.keys(entityFields).map(field => ({
      value: field,
      label: field.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
    }));
  };

  // Get field type
  const getFieldType = (entity: string, field: string) => {
    return FIELD_TYPES[entity as keyof typeof FIELD_TYPES]?.[field as keyof typeof FIELD_TYPES[keyof typeof FIELD_TYPES]] || 'text';
  };

  // Enhanced operator options based on field type
  const getOperatorOptions = (entity: string, field: string) => {
    const fieldType = getFieldType(entity, field);
    
    switch (fieldType) {
      case 'text':
        return [
          { value: "equals", label: "Equals" },
          { value: "not_equals", label: "Not equals" },
          { value: "contains", label: "Contains" },
          { value: "not_contains", label: "Does not contain" },
          { value: "starts_with", label: "Starts with" },
          { value: "ends_with", label: "Ends with" },
          { value: "is_empty", label: "Is empty" },
          { value: "is_not_empty", label: "Is not empty" },
          { value: "is_null", label: "Is null" },
          { value: "is_not_null", label: "Is not null" },
        ];
      
      case 'number':
        return [
          { value: "equals", label: "Equals" },
          { value: "not_equals", label: "Not equals" },
          { value: "greater_than", label: "Greater than" },
          { value: "greater_than_equal", label: "Greater than or equal" },
          { value: "less_than", label: "Less than" },
          { value: "less_than_equal", label: "Less than or equal" },
          { value: "between", label: "Between (min,max)" },
          { value: "is_null", label: "Is null" },
          { value: "is_not_null", label: "Is not null" },
        ];
      
      case 'date':
        return [
          { value: "equals", label: "Equals" },
          { value: "before", label: "Before" },
          { value: "after", label: "After" },
          { value: "on_or_before", label: "On or before" },
          { value: "on_or_after", label: "On or after" },
          { value: "between", label: "Between (start,end)" },
          { value: "is_null", label: "Is null" },
          { value: "is_not_null", label: "Is not null" },
        ];
      
      case 'boolean':
        return [
          { value: "is_true", label: "Is true" },
          { value: "is_false", label: "Is false" },
          { value: "is_null", label: "Is null" },
          { value: "is_not_null", label: "Is not null" },
        ];
      
      default:
        return [
          { value: "equals", label: "Equals" },
          { value: "not_equals", label: "Not equals" },
        ];
    }
  };

  // Check if operator needs a value input
  const operatorNeedsValue = (operator: string) => {
    const noValueOperators = ['is_null', 'is_not_null', 'is_empty', 'is_not_empty', 'is_true', 'is_false'];
    return !noValueOperators.includes(operator);
  };

  // Render appropriate input based on field type
  const renderValueInput = (condition: FilterCondition) => {
    const fieldType = getFieldType(condition.entity, condition.field);
    const needsValue = operatorNeedsValue(condition.operator);
    
    if (!needsValue) {
      return (
        <Input
          value="(no value needed)"
          disabled
          className="bg-muted"
        />
      );
    }
    
    switch (fieldType) {
      case 'date':
        return (
          <DatePicker
            value={condition.value}
            onChange={(value) => updateCondition(condition.id, "value", value)}
          />
        );
      
      case 'number':
        return (
          <Input
            type="number"
            value={condition.value}
            onChange={(e) => updateCondition(condition.id, "value", e.target.value)}
            placeholder={condition.operator === 'between' ? "min,max" : "Enter number"}
          />
        );
      
      case 'boolean':
        return (
          <Select
            value={condition.value}
            onValueChange={(value) => updateCondition(condition.id, "value", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select value" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">True</SelectItem>
              <SelectItem value="false">False</SelectItem>
            </SelectContent>
          </Select>
        );
      
      default:
        return (
          <Input
            value={condition.value}
            onChange={(e) => updateCondition(condition.id, "value", e.target.value)}
            placeholder="Enter value"
          />
        );
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Advanced Filters</CardTitle>
          <CardDescription>
            Build complex filters with multiple conditions to query your data effectively.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleFilterSubmit} className="space-y-6">
            {/* Entity Selection */}
            <div className="space-y-2">
              <Label>Select Table</Label>
              <Select
                value={selectedEntity}
                onValueChange={(value) => {
                  setSelectedEntity(value);
                  // Update all conditions to use the new entity
                  setConditions(conditions.map(c => ({ ...c, entity: value, field: "name" })));
                }}
              >
                <SelectTrigger className="w-1/3">
                  <SelectValue placeholder="Select table" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="students">Students</SelectItem>
                  <SelectItem value="courses">Courses</SelectItem>
                  <SelectItem value="payments">Payments</SelectItem>
                  <SelectItem value="enrollments">Enrollments</SelectItem>
                  <SelectItem value="employees">Employees</SelectItem>
                  <SelectItem value="departments">Departments</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Show All Data Option */}
            <div className="space-y-3">
              <Label>Filter Options</Label>
              <RadioGroup 
                value={showAllData ? "all" : "filtered"} 
                onValueChange={(value) => setShowAllData(value === "all")}
                className="flex items-center space-x-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="filtered" id="filtered" />
                  <Label htmlFor="filtered">Apply Filters</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="all" />
                  <Label htmlFor="all">Show All Data</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Filter Conditions */}
            {!showAllData && (
              <div className="space-y-4">
                <Label>Filter Conditions</Label>
                {conditions.map((condition) => (
                  <div key={condition.id} className="flex items-center gap-3">
                    <div className="w-1/5">
                      <Select
                        value={condition.field}
                        onValueChange={(value) => updateCondition(condition.id, "field", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select field" />
                        </SelectTrigger>
                        <SelectContent>
                          {getFieldOptions(condition.entity).map(option => (
                            <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="w-1/4">
                      <Select
                        value={condition.operator}
                        onValueChange={(value) => updateCondition(condition.id, "operator", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select operator" />
                        </SelectTrigger>
                        <SelectContent>
                          {getOperatorOptions(condition.entity, condition.field).map(option => (
                            <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex-1">
                      {renderValueInput(condition)}
                    </div>
                    
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeCondition(condition.id)}
                      disabled={conditions.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addCondition}
                  className="flex items-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  Add Condition
                </Button>
              </div>
            )}
            
            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Search className="h-4 w-4 mr-2" />}
                {showAllData ? 'Get All Data' : 'Apply Filters'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      
      {isLoading && (
        <div className="flex justify-center my-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}
      
      {results && results.length > 0 && !isLoading && (
        <Card>
          <CardHeader>
            <CardTitle>Filter Results</CardTitle>
            <CardDescription>
              Found {results.length} result{results.length !== 1 ? 's' : ''} from {selectedEntity}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <div className="max-h-[500px] overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {Object.keys(results[0]).map((key) => (
                        <TableHead key={key} className="whitespace-nowrap">
                          {key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.map((row, i) => (
                      <TableRow key={i}>
                        {Object.entries(row).map(([key, value]) => (
                          <TableCell key={key}>
                            {value === null ? (
                              <span className="text-muted-foreground italic">null</span>
                            ) : typeof value === 'boolean' ? (
                              <span className={value ? "text-green-600" : "text-red-600"}>
                                {value ? "true" : "false"}
                              </span>
                            ) : typeof value === 'object' ? (
                              <code className="text-xs bg-muted px-1 rounded">
                                {JSON.stringify(value)}
                              </code>
                            ) : (
                              String(value)
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {results && results.length === 0 && !isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <p className="text-muted-foreground">No results match your filter criteria</p>
              <p className="text-sm text-muted-foreground mt-2">Try adjusting your filters or select "Show All Data"</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
