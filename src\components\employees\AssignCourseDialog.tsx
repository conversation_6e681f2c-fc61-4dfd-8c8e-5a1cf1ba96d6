
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useCourses } from "@/hooks/useCourses";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { UserPlus } from "lucide-react";

interface AssignCourseDialogProps {
  employeeId: string;
  employeeName: string;
  assignedCourseIds: string[];
  onCourseAssigned: () => void;
}

export function AssignCourseDialog({ 
  employeeId, 
  employeeName, 
  assignedCourseIds,
  onCourseAssigned 
}: AssignCourseDialogProps) {
  const [open, setOpen] = useState(false);
  const [selectedCourseId, setSelectedCourseId] = useState<string>("");
  const [isAssigning, setIsAssigning] = useState(false);
  const { data: courses = [], isLoading: coursesLoading } = useCourses();

  // Filter out already assigned courses
  const availableCourses = courses.filter(
    course => !assignedCourseIds.includes(course.id)
  );

  const handleAssignCourse = async () => {
    if (!selectedCourseId) return;
    
    setIsAssigning(true);
    
    try {
      const { error } = await supabase
        .from("teaches")
        .insert([{ course_id: selectedCourseId, employee_id: employeeId }]);
      
      if (error) throw error;
      
      toast({
        title: "Course assigned!",
        description: `Course has been assigned to ${employeeName}`,
      });
      
      setSelectedCourseId("");
      setOpen(false);
      onCourseAssigned();
    } catch (error) {
      console.error("Error assigning course:", error);
      toast({
        title: "Error",
        description: "Failed to assign course",
        variant: "destructive",
      });
    } finally {
      setIsAssigning(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Assign Course
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Assign Course to {employeeName}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Select Course</label>
            <Select
              value={selectedCourseId}
              onValueChange={setSelectedCourseId}
              disabled={isAssigning || coursesLoading}
            >
              <SelectTrigger className="w-full mt-2">
                <SelectValue placeholder="Choose a course to assign" />
              </SelectTrigger>
              <SelectContent>
                {availableCourses.length === 0 ? (
                  <div className="p-2 text-sm text-muted-foreground">
                    No available courses to assign
                  </div>
                ) : (
                  availableCourses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      <div className="flex flex-col">
                        <span>{course.title}</span>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {course.department?.name || 'No Department'}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {course.duration_months} months
                          </Badge>
                        </div>
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isAssigning}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAssignCourse}
              disabled={!selectedCourseId || isAssigning}
            >
              {isAssigning ? "Assigning..." : "Assign Course"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
