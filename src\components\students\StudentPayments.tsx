
import { Payment } from "@/hooks/usePayments";
import { Card } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getPaymentStatusColor } from "@/utils/statusColorUtils";

interface StudentPaymentsProps {
  payments: Payment[];
  onUpdatePaymentStatus: (paymentId: string, status: 'pending' | 'paid' | 'overdue' | 'partial') => Promise<any>;
}

export const StudentPayments = ({ payments, onUpdatePaymentStatus }: StudentPaymentsProps) => {
  return (
    <div>
      <h2 className="text-2xl font-semibold mb-4">Payments History</h2>
      <Card>
        {payments.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Amount</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.map(payment => (
                <TableRow key={payment.id}>
                  <TableCell>${payment.amount.toFixed(2)}</TableCell>
                  <TableCell className="capitalize">
                    {payment.method.replace('_', ' ')}
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant="outline" 
                      className={getPaymentStatusColor(payment.status)}
                    >
                      {payment.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Select 
                      value={payment.status}
                      onValueChange={(status) => 
                        onUpdatePaymentStatus(payment.id, status as 'pending' | 'paid' | 'overdue' | 'partial')
                      }
                    >
                      <SelectTrigger className="w-[100px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="partial">Partial</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                        <SelectItem value="overdue">Overdue</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="p-6">
            <p className="text-muted-foreground text-center">No payment records found</p>
          </div>
        )}
      </Card>
    </div>
  );
};
