
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { TrendingUp, TrendingDown } from "lucide-react";
import React from "react";

type StatCardProps = {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
  style?: React.CSSProperties;
};

export function StatCard({ 
  title, 
  value, 
  description, 
  icon, 
  trend, 
  className,
  style
}: StatCardProps) {
  return (
    <Card 
      className={cn(
        "overflow-hidden border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 hover:-translate-y-2 group cursor-pointer transform-gpu", 
        className
      )}
      style={style}
    >
      <CardContent className="p-6 relative">
        {/* Animated background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        
        <div className="flex items-start justify-between mb-4 relative z-10">
          <div className="space-y-1">
            <p className="text-sm font-bold text-slate-600/90 dark:text-slate-300/90 uppercase tracking-wide transform transition-all duration-300 group-hover:tracking-wider">
              {title}
            </p>
            <div className="flex items-baseline gap-2">
              <h3 className="text-3xl font-black text-slate-900 dark:text-white group-hover:text-slate-800 dark:group-hover:text-slate-100 transition-all duration-300 transform group-hover:scale-110">
                {value}
              </h3>
              {trend && (
                <div className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold backdrop-blur-sm border transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-1",
                  trend.isPositive 
                    ? "text-emerald-700 bg-emerald-100/80 dark:text-emerald-300 dark:bg-emerald-900/40 border-emerald-200/50 dark:border-emerald-400/30" 
                    : "text-red-700 bg-red-100/80 dark:text-red-300 dark:bg-red-900/40 border-red-200/50 dark:border-red-400/30"
                )}>
                  <div className="transform transition-transform duration-300 group-hover:animate-bounce">
                    {trend.isPositive ? (
                      <TrendingUp className="w-3 h-3" />
                    ) : (
                      <TrendingDown className="w-3 h-3" />
                    )}
                  </div>
                  <span className="tabular-nums">{trend.value}%</span>
                </div>
              )}
            </div>
            {description && (
              <p className="text-sm text-slate-600 dark:text-slate-400 font-medium transition-all duration-300 group-hover:text-slate-700 dark:group-hover:text-slate-300">
                {description}
              </p>
            )}
          </div>
          {icon && (
            <div className="backdrop-blur-sm bg-white/40 dark:bg-black/20 p-3 rounded-xl border border-white/30 dark:border-white/10 text-slate-700 dark:text-slate-300 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 transform-gpu group-hover:shadow-lg">
              <div className="transform transition-transform duration-500 group-hover:animate-pulse">
                {icon}
              </div>
            </div>
          )}
        </div>
        
        {/* Enhanced bottom accent with animation */}
        <div className="h-1 w-full bg-gradient-to-r from-transparent via-white/40 to-transparent dark:via-white/20 rounded-full opacity-60 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/50 via-purple-500/50 to-pink-500/50 transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
        </div>
        
        {/* Floating particles effect */}
        <div className="absolute top-2 right-2 w-1 h-1 bg-blue-400/60 rounded-full opacity-0 group-hover:opacity-100 transform group-hover:animate-ping transition-all duration-500"></div>
        <div className="absolute top-4 right-6 w-1 h-1 bg-purple-400/60 rounded-full opacity-0 group-hover:opacity-100 transform group-hover:animate-ping transition-all duration-700 delay-100"></div>
        <div className="absolute top-6 right-3 w-1 h-1 bg-pink-400/60 rounded-full opacity-0 group-hover:opacity-100 transform group-hover:animate-ping transition-all duration-600 delay-200"></div>
      </CardContent>
    </Card>
  );
}
