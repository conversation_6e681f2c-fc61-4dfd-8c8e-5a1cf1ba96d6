
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { StudentWithPayments } from "@/hooks/useStudents";
import { StudentTableRow } from "./StudentTableRow";

interface StudentTableProps {
  students: StudentWithPayments[];
  isLoading: boolean;
  onStudentDeleted: () => void;
}

export function StudentTable({ students, isLoading, onStudentDeleted }: StudentTableProps) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader className="bg-muted/50">
          <TableRow className="hover:bg-transparent">
            <TableHead className="font-semibold">Name</TableHead>
            <TableHead className="font-semibold">Registration Date</TableHead>
            <TableHead className="font-semibold">Courses Enrolled</TableHead>
            <TableHead className="font-semibold">Payment Status</TableHead>
            <TableHead className="w-14 font-semibold"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell
                colSpan={6}
                className="text-center py-6 text-muted-foreground"
              >
                Loading students...
              </TableCell>
            </TableRow>
          ) : students.length > 0 ? (
            students.map((student) => (
              <StudentTableRow 
                key={student.id} 
                student={student}
                onStudentDeleted={onStudentDeleted}
              />
            ))
          ) : (
            <TableRow>
              <TableCell
                colSpan={6}
                className="text-center py-6 text-muted-foreground"
              >
                No students found matching your search criteria.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
