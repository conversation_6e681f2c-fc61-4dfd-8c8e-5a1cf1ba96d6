import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BookOpen, DollarSign, MessageSquare, UserPlus, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { cn } from "@/lib/utils";

export function QuickActions() {
  const navigate = useNavigate();
  
  const actions = [
    {
      title: "Add Student",
      description: "Enroll new student",
      icon: <UserPlus className="w-5 h-5" />,
      onClick: () => navigate("/students/new"),
      iconColor: "text-blue-600 dark:text-blue-400",
      iconBg: "bg-blue-500/20"
    },
    {
      title: "Add Course",
      description: "Create new course",
      icon: <BookOpen className="w-5 h-5" />,
      onClick: () => navigate("/courses/new"),
      iconColor: "text-emerald-600 dark:text-emerald-400",
      iconBg: "bg-emerald-500/20"
    },
    {
      title: "Record Payment",
      description: "Process payment",
      icon: <DollarSign className="w-5 h-5" />,
      onClick: () => navigate("/payments/new"),
      iconColor: "text-amber-600 dark:text-amber-400",
      iconBg: "bg-amber-500/20"
    },
    {
      title: "AI Assistant",
      description: "Ask questions",
      icon: <MessageSquare className="w-5 h-5" />,
      onClick: () => navigate("/query"),
      iconColor: "text-purple-600 dark:text-purple-400",
      iconBg: "bg-purple-500/20"
    },
  ];
  
  return (
    <Card className="border-0 shadow-none bg-transparent">
      <CardHeader className="pb-6">
        <div className="flex items-center gap-2">
          <div className="backdrop-blur-md bg-white/20 dark:bg-white/10 p-2 rounded-lg border border-white/30 dark:border-white/20 transform transition-all duration-300 hover:scale-110 hover:rotate-6">
            <Plus className="w-4 h-4 text-slate-700 dark:text-slate-200 transform transition-transform duration-300 hover:rotate-180" />
          </div>
          <div>
            <CardTitle className="text-xl font-black text-slate-900 dark:text-white">Quick Actions</CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-400 font-medium">
              Fast-track your most common tasks
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {actions.map((action, index) => (
          <Button
            key={index}
            variant="ghost"
            className={cn(
              "w-full h-auto flex items-center justify-start p-4 gap-4 text-left backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 transition-all duration-300 hover:scale-105 hover:-translate-y-1 group relative overflow-hidden transform-gpu",
              "hover:bg-white/15 dark:hover:bg-white/8 hover:shadow-xl hover:border-white/40 dark:hover:border-white/30"
            )}
            onClick={action.onClick}
            style={{
              animationDelay: `${index * 100}ms`,
            }}
          >
            {/* Glassmorphism shimmer effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out"></div>
            
            <div className={cn(
              "p-3 rounded-xl backdrop-blur-md border border-white/30 dark:border-white/20 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300 transform-gpu relative z-10",
              action.iconBg,
              "group-hover:shadow-lg group-hover:bg-white/30 dark:group-hover:bg-white/15"
            )}>
              <div className={cn(
                "transform transition-all duration-300 group-hover:animate-bounce",
                action.iconColor
              )}>
                {action.icon}
              </div>
            </div>
            <div className="flex-1 relative z-10">
              <div className="font-bold text-slate-900 dark:text-white text-sm group-hover:text-slate-800 dark:group-hover:text-slate-100 transition-all duration-300 transform group-hover:translate-x-1">
                {action.title}
              </div>
              <div className="text-xs text-slate-600 dark:text-slate-400 font-medium transition-all duration-300 group-hover:text-slate-700 dark:group-hover:text-slate-300 transform group-hover:translate-x-1">
                {action.description}
              </div>
            </div>
            
            {/* Glass ripple effect */}
            <div className="absolute inset-0 rounded-xl opacity-0 group-active:opacity-20 bg-white/20 dark:bg-white/10 transition-opacity duration-150 pointer-events-none"></div>
          </Button>
        ))}
      </CardContent>
    </Card>
  );
}