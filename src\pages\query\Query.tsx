
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { MessageSquare, Filter, Database } from "lucide-react";
import { QueryAssistant } from "@/components/query/QueryAssistant";
import { AdvancedFilters } from "@/components/query/AdvancedFilters";
import { PrebuiltQueries } from "@/components/query/PrebuiltQueries";

export default function Query() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Query Section</h1>
          <p className="text-muted-foreground">
            Search and filter data using natural language, advanced filters, or prebuilt queries
          </p>
        </div>
      </div>
      
      <Tabs defaultValue="assistant" className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-4">
          <TabsTrigger value="assistant" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span>Query Assistant</span>
          </TabsTrigger>
          <TabsTrigger value="prebuilt" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            <span>Prebuilt Queries</span>
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span>Advanced Filters</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="assistant" className="mt-0">
          <QueryAssistant />
        </TabsContent>
        
        <TabsContent value="prebuilt" className="mt-0">
          <PrebuiltQueries />
        </TabsContent>
        
        <TabsContent value="advanced" className="mt-0">
          <AdvancedFilters />
        </TabsContent>
      </Tabs>
    </div>
  );
}
