
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { useSidebar } from "@/components/ui/sidebar";
import { 
  BookOpen, 
  Users, 
  GraduationCap, 
  DollarSign, 
  Settings, 
  Home, 
  Menu, 
  ChevronLeft,
  Search,
  BarChart
} from "lucide-react";

type NavItemProps = {
  icon: React.ElementType;
  label: string;
  href: string;
  isActive: boolean;
  isCollapsed: boolean;
};

const NavItem = ({ icon: Icon, label, href, isActive, isCollapsed }: NavItemProps) => {
  return (
    <Link
      to={href}
      className={cn(
        // Increased gap, vertical padding, and reduced font size
        "flex items-center gap-5 px-4 py-3 mx-1 my-0.5 rounded-lg text-[13px] transition-all duration-300 relative overflow-hidden",
        isCollapsed ? "justify-center px-0 mx-0" : "",
        isActive
          ? "text-sidebar-accent-foreground font-medium"
          : "text-sidebar-foreground hover:bg-sidebar-accent/40 hover:text-sidebar-accent-foreground",
        "focus:outline-none focus:ring-2 focus:ring-sidebar-ring"
      )}
    >
      {isActive && (
        <span 
          className="absolute inset-0 bg-white/10 backdrop-blur-md border border-white/10 rounded-lg -z-10"
          style={{
            boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)'
          }}
        />
      )}
      <Icon className="h-5 w-5" />
      {!isCollapsed && <span className="leading-5">{label}</span>}
    </Link>
  );
};

export function Sidebar() {
  const { collapsed: isCollapsed, setCollapsed } = useSidebar();
  const location = useLocation();

  const navItems = [
    { icon: Home, label: "Dashboard", href: "/" },
    { icon: Users, label: "Students", href: "/students" },
    { icon: BookOpen, label: "Courses", href: "/courses" },
    { icon: GraduationCap, label: "Employees", href: "/employees" },
    { icon: DollarSign, label: "Payments", href: "/payments" },
    { icon: Search, label: "Query", href: "/query" },
    { icon: Settings, label: "Settings", href: "/settings" },
  ];

  return (
    <div
      className={cn(
        "fixed left-0 top-0 h-screen bg-sidebar flex flex-col transition-all duration-300 z-40",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      <div className={cn("p-4 flex items-center", isCollapsed ? "justify-center" : "justify-between")}>
        {!isCollapsed && (
          <span className="text-[1.3rem] font-extrabold" style={{ fontWeight: 700, letterSpacing: "0.5px" }}>
            Enroll Matrix
          </span>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setCollapsed(!isCollapsed)}
          className="rounded-full"
        >
          {isCollapsed ? <Menu className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
        </Button>
      </div>
      
      
      
      <ScrollArea className="flex-1 py-4">
        <nav className="grid gap-1 px-2">
          {navItems.map((item) => (
            <NavItem
              key={item.href}
              icon={item.icon}
              label={item.label}
              href={item.href}
              isActive={location.pathname === item.href}
              isCollapsed={isCollapsed}
            />
          ))}
        </nav>
      </ScrollArea>
      
      
      
      {!isCollapsed && (
        <div className="p-4 text-xs text-muted-foreground">
          <p>EduLearn Admin v1.0</p>
        </div>
      )}
    </div>
  );
}
