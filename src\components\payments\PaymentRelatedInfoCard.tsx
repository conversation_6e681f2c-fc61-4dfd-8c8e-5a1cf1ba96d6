
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, BookOpen } from "lucide-react";
import { Payment } from "@/hooks/usePayments";

interface PaymentRelatedInfoCardProps {
  payment: Payment;
  onStatusChange: (status: 'pending' | 'paid' | 'overdue' | 'partial') => void;
}

export function PaymentRelatedInfoCard({ payment, onStatusChange }: PaymentRelatedInfoCardProps) {
  const navigate = useNavigate();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Related Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div 
          className="flex items-center gap-3 p-3 border rounded-md hover:bg-muted/50 cursor-pointer transition-colors"
          onClick={() => payment.student?.name && navigate(`/students/${payment.student_id}`)}
        >
          <User className="h-5 w-5 text-muted-foreground" />
          <div>
            <p className="font-medium">Student</p>
            <p className="text-sm text-muted-foreground">
              {payment.student?.name || 'Unknown'}
            </p>
          </div>
        </div>
        
        {payment.course && (
          <div 
            className="flex items-center gap-3 p-3 border rounded-md hover:bg-muted/50 cursor-pointer transition-colors"
            onClick={() => payment.course?.title && navigate(`/courses/${payment.course_id}`)}
          >
            <BookOpen className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="font-medium">Course</p>
              <p className="text-sm text-muted-foreground">
                {payment.course?.title || 'General Payment'}
              </p>
            </div>
          </div>
        )}
        
        <div className="space-y-2">
          <p className="font-medium">Update Payment Status</p>
          <div className="flex flex-wrap gap-2">
            <Button 
              size="sm" 
              variant={payment.status === 'pending' ? 'default' : 'outline'}
              onClick={() => onStatusChange('pending')}
            >
              Pending
            </Button>
            <Button 
              size="sm" 
              variant={payment.status === 'partial' ? 'default' : 'outline'}
              onClick={() => onStatusChange('partial')}
            >
              Partial
            </Button>
            <Button 
              size="sm" 
              variant={payment.status === 'paid' ? 'default' : 'outline'}
              onClick={() => onStatusChange('paid')}
            >
              Paid
            </Button>
            <Button 
              size="sm" 
              variant={payment.status === 'overdue' ? 'default' : 'outline'}
              onClick={() => onStatusChange('overdue')}
            >
              Overdue
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
