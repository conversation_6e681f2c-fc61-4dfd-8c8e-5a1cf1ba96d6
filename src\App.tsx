
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { DashboardLayout } from "./components/layout/DashboardLayout";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useTheme } from "@/hooks/use-theme";

// Auth pages
import Login from "./pages/auth/Login";

// Dashboard pages
import Dashboard from "./pages/dashboard/Dashboard";
import Students from "./pages/students/Students";
import Courses from "./pages/courses/Courses";
import Employees from "./pages/employees/Employees";
import Payments from "./pages/payments/Payments";
import PaymentDetails from "./pages/payments/PaymentDetails";
import Query from "./pages/query/Query";
import Settings from "./pages/settings/Settings";
import NotFound from "./pages/NotFound";
import StudentDetails from "./pages/students/StudentDetails";
import EditStudent from "./pages/students/EditStudent";
import CourseDetails from "./pages/courses/CourseDetails";
import EditCourse from "./pages/courses/EditCourse";
import CourseStudents from "./pages/courses/CourseStudents";

// Employee pages
import EmployeeDetails from "./pages/employees/EmployeeDetails";
import EditEmployee from "./pages/employees/EditEmployee";
import EmployeeAssignedCourses from "./pages/employees/EmployeeAssignedCourses";

const queryClient = new QueryClient();

const App = () => {
  useTheme();

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <SidebarProvider>
            <Routes>
              {/* Auth Routes */}
              <Route path="/login" element={<Login />} />

              {/* Dashboard Routes */}
              <Route element={<DashboardLayout />}>
                <Route path="/" element={<Dashboard />} />
                <Route path="/students" element={<Students />} />
                <Route path="/courses" element={<Courses />} />
                <Route path="/employees" element={<Employees />} />
                <Route path="/payments" element={<Payments />} />
                <Route path="/payments/:paymentId" element={<PaymentDetails />} />
                <Route path="/query" element={<Query />} />
                <Route path="/settings" element={<Settings />} />
              </Route>

              {/* Student Routes */}
              <Route path="/students/:studentId" element={<StudentDetails />} />
              <Route path="/students/:studentId/edit" element={<EditStudent />} />

              {/* Course Routes */}
              <Route path="/courses/:courseId" element={<CourseDetails />} />
              <Route path="/courses/:courseId/edit" element={<EditCourse />} />
              <Route path="/courses/:courseId/students" element={<CourseStudents />} />

              {/* Employee Routes */}
              <Route path="/employees/:employeeId" element={<EmployeeDetails />} />
              <Route path="/employees/:employeeId/edit" element={<EditEmployee />} />
              <Route path="/employees/:employeeId/courses" element={<EmployeeAssignedCourses />} />

              {/* Redirect to dashboard from index */}
              <Route path="/index" element={<Navigate to="/" replace />} />

              {/* Catch-all route for 404 */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </SidebarProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
