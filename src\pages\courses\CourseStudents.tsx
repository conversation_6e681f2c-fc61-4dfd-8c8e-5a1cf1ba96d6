
import { useParams, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { useCourseDetails } from "@/hooks/useCourseDetails";
import { useStudents } from "@/hooks/useStudents";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Plus, Search, Trash2, User } from "lucide-react";
import { useEnrollments } from "@/hooks/useEnrollments";
import { format } from "date-fns";

export default function CourseStudents() {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStudentId, setSelectedStudentId] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const { 
    courseDetails, 
    loading, 
    fetchCourseDetails,
    removeStudent
  } = useCourseDetails();
  
  const { data: allStudents = [] } = useStudents();
  const { enrollStudent } = useEnrollments(courseId);

  useEffect(() => {
    const loadData = async () => {
      if (courseId) {
        setIsLoading(true);
        await fetchCourseDetails(courseId);
        setIsLoading(false);
      }
    };
    
    loadData();
  }, [courseId, fetchCourseDetails]);

  const showLoading = isLoading || loading || !courseDetails;

  const filteredStudents = courseDetails && courseDetails.students
    ? courseDetails.students.filter(student => 
        student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.email.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  const availableStudents = allStudents.filter(student => 
    !courseDetails?.students?.some(enrolled => enrolled.id === student.id) || false
  );

  const handleRemoveStudent = async (enrollmentId: string) => {
    if (courseId) {
      await removeStudent(enrollmentId, courseId);
    }
  };

  const handleEnrollStudent = async () => {
    if (courseId && selectedStudentId) {
      await enrollStudent(selectedStudentId, courseId);
      await fetchCourseDetails(courseId);
      setDialogOpen(false);
      setSelectedStudentId("");
    }
  };

  if (showLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Loading course students...</div>
        </div>
      </div>
    );
  }

  if (!courseDetails) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Course not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <Button 
          variant="outline" 
          onClick={() => navigate(`/courses/${courseId}`)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Course Details
        </Button>
        <h1 className="text-2xl font-bold">{courseDetails.title} - Students</h1>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Enroll Student
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Enroll a New Student</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <label htmlFor="student-select" className="text-sm font-medium">
                  Select Student
                </label>
                <Select
                  onValueChange={setSelectedStudentId}
                  value={selectedStudentId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a student" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableStudents.length === 0 ? (
                      <SelectItem value="no-students" disabled>
                        No available students
                      </SelectItem>
                    ) : (
                      availableStudents.map((student) => (
                        <SelectItem key={student.id} value={student.id}>
                          {student.name} ({student.email})
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end">
                <Button 
                  onClick={handleEnrollStudent}
                  disabled={!selectedStudentId || selectedStudentId === "no-students"}
                >
                  Enroll Student
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="flex items-center space-x-2">
        <Search className="h-5 w-5 text-muted-foreground" />
        <Input
          placeholder="Search students..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Enrolled Students</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Enrolled On</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-10 text-muted-foreground">
                    {searchQuery ? 'No students match your search criteria' : 'No students enrolled in this course'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredStudents.map((student) => (
                  <TableRow key={student.enrollment_id}>
                    <TableCell>{student.name}</TableCell>
                    <TableCell>{student.email}</TableCell>
                    <TableCell>
                      {format(new Date(student.enrolled_on), 'MMM dd, yyyy')}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => navigate(`/students/${student.id}`)}
                        >
                          <User className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveStudent(student.enrollment_id)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
