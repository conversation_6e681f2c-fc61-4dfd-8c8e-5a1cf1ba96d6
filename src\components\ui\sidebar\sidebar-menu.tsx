
import * as React from "react"
import { cn } from "@/lib/utils"
import { useSidebar } from "./sidebar-context"

export const SidebarMenu = React.forwardRef<
  HTMLUListElement,
  React.HTMLAttributes<HTMLUListElement>
>(({ className, ...props }, ref) => {
  return (
    <ul
      ref={ref}
      className={cn("space-y-1", className)}
      {...props}
    />
  )
})
SidebarMenu.displayName = "SidebarMenu"

export const SidebarMenuItem = React.forwardRef<
  HTMLLIElement,
  React.HTMLAttributes<HTMLLIElement>
>(({ className, ...props }, ref) => {
  return (
    <li
      ref={ref}
      className={cn("", className)}
      {...props}
    />
  )
})
SidebarMenuItem.displayName = "SidebarMenuItem"

export const SidebarMenuButton = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    asChild?: boolean
  }
>(({ className, asChild = false, ...props }, ref) => {
  const { collapsed } = useSidebar()
  
  const Comp = asChild ? React.Fragment : "button"
  const compProps = asChild ? {} : props

  return (
    <Comp
      ref={ref}
      className={cn(
        "flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors w-full",
        "hover:bg-accent hover:text-accent-foreground",
        {
          "justify-center": collapsed,
          "": !collapsed
        },
        className
      )}
      {...compProps}
    >
      {asChild ? props.children : null}
    </Comp>
  )
})
SidebarMenuButton.displayName = "SidebarMenuButton"
