import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2, Send } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { QueryResultsChart } from "@/components/query/QueryResultsChart";
import { supabase } from "@/integrations/supabase/client";

interface Message {
  id: string;
  content: string;
  role: "assistant" | "user";
  timestamp: Date;
  isLoading?: boolean;
}

interface QueryResult {
  results?: any[];
  sql?: string;
  chartType?: string;
  chartData?: any;
  timestamp: Date;
}

export function QueryAssistant() {
  const { toast } = useToast();
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      content: "Hello! I'm your EduLearn Query Assistant. How can I help you find information about your school data today?",
      role: "assistant",
      timestamp: new Date(),
    },
  ]);
  const [currentResult, setCurrentResult] = useState<QueryResult | null>(null);
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState(0);

  const suggestionQueries = [
    {
      category: "Students",
      queries: [
        "List all students",
        "Find students enrolled in Mathematics",
        "Show me students with pending payments",
        "How many students are enrolled in each course?",
      ]
    },
    {
      category: "Courses",
      queries: [
        "Show me all courses",
        "Show courses in Computer Science department",
        "Which departments have the most courses?",
        "What are the statistics on course durations?",
      ]
    },
    {
      category: "Teachers & Employees",
      queries: [
        "List teachers with their assigned courses",
        "Show me all employees with role 'teacher'",
        "List employees in the Finance department",
        "What's the teacher to student ratio?",
      ]
    },
    {
      category: "Payments",
      queries: [
        "What's the distribution of payment methods?",
        "Show me payment status distribution",
        "Show me students with pending payments",
        "Show total revenue by department",
      ]
    },
  ];

  // Flatten all queries into a single array for rotation
  const allQueries = suggestionQueries.flatMap(category => 
    category.queries.map(query => ({
      category: category.category,
      query
    }))
  );

  // Update suggestion every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSuggestionIndex((prev) => (prev + 1) % allQueries.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input.trim(),
      role: "user",
      timestamp: new Date(),
    };

    const loadingMessage: Message = {
      id: "loading",
      content: "Analyzing your query...",
      role: "assistant",
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    setInput("");
    setIsLoading(true);

    try {
      const { data, error } = await supabase.functions.invoke('query-assistant', {
        body: { query: input.trim() }
      });

      if (error) throw error;

      const responseMessage: Message = {
        id: Date.now().toString(),
        content: "I've found some results for your query. You can see them in detail below.",
        role: "assistant",
        timestamp: new Date(),
      };

      setCurrentResult({
        results: data.results,
        sql: data.sql,
        chartType: data.chartType,
        chartData: data.chartData,
        timestamp: new Date(),
      });

      setMessages(prev => prev.filter(m => !m.isLoading).concat(responseMessage));
    } catch (err) {
      console.error('Error executing query:', err);
      toast({
        variant: "destructive",
        title: "Query error",
        description: (err as Error).message || "Failed to process your query"
      });
      setMessages(prev => prev.filter(m => !m.isLoading));
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const handleSuggestionClick = (query: string) => {
    setInput(query);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-4 gap-4">
        {/* Chat Interface */}
        <div className="col-span-3">
          <div className="flex flex-col h-[calc(100vh-17rem)] border rounded-lg bg-background">
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex",
                    message.role === "user" ? "justify-end" : "justify-start"
                  )}
                >
                  <div
                    className={cn(
                      "max-w-[80%] rounded-lg px-4 py-2",
                      message.role === "user"
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted",
                      message.isLoading && "animate-pulse"
                    )}
                  >
                    {message.isLoading ? (
                      <div className="flex items-center space-x-2">
                        <Loader2 className="h-5 w-5 animate-spin" />
                        <span>{message.content}</span>
                      </div>
                    ) : (
                      <>
                        <div className="whitespace-pre-wrap">{message.content}</div>
                        <div className="text-xs mt-1 opacity-70">
                          {formatTime(message.timestamp)}
                        </div>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex-none p-4 border-t">
              <form onSubmit={handleSubmit} className="flex space-x-2">
                <Input
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Type your question..."
                  className="flex-1"
                  disabled={isLoading}
                />
                <Button type="submit" size="icon" disabled={isLoading}>
                  <Send className="h-4 w-4" />
                  <span className="sr-only">Send message</span>
                </Button>
              </form>
            </div>
          </div>

          {/* Results Box */}
          {currentResult && (
            <div className="space-y-4 mt-4">
              {currentResult.sql && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Generated SQL Query</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
                      <code>{currentResult.sql}</code>
                    </pre>
                  </CardContent>
                </Card>
              )}

              {currentResult.chartType && currentResult.chartData && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">Data Visualization</CardTitle>
                  </CardHeader>
                  <CardContent className="h-64">
                    <QueryResultsChart 
                      type={currentResult.chartType} 
                      data={currentResult.chartData} 
                    />
                  </CardContent>
                </Card>
              )}

              {currentResult.results && currentResult.results.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium">
                      Query Results
                      <span className="text-muted-foreground ml-2">
                        ({currentResult.results.length} {currentResult.results.length === 1 ? 'row' : 'rows'})
                      </span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <div className="max-h-[400px] overflow-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              {Object.keys(currentResult.results[0]).map((key) => (
                                <TableHead key={key}>{key}</TableHead>
                              ))}
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {currentResult.results.map((row, i) => (
                              <TableRow key={i}>
                                {Object.entries(row).map(([key, value]) => (
                                  <TableCell key={key}>
                                    {value === null ? (
                                      <span className="text-muted-foreground">null</span>
                                    ) : typeof value === 'object' ? (
                                      JSON.stringify(value)
                                    ) : (
                                      String(value)
                                    )}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {currentResult.results && currentResult.results.length === 0 && (
                <Card>
                  <CardContent className="py-8">
                    <div className="text-center text-muted-foreground">
                      No results found for your query
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>

        {/* Suggestions Box */}
        <div className="col-span-1">
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle className="text-sm font-medium">Try Asking</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative p-4 rounded-lg bg-muted/50">
                  <div className="text-xs font-medium text-muted-foreground mb-2">
                    {allQueries[currentSuggestionIndex].category}
                  </div>
                  <button
                    onClick={() => handleSuggestionClick(allQueries[currentSuggestionIndex].query)}
                    className="text-sm font-medium hover:text-primary transition-colors duration-200"
                  >
                    {allQueries[currentSuggestionIndex].query}
                  </button>
                  <div className="absolute bottom-2 right-2 flex gap-1">
                    {Array.from({ length: 4 }, (_, i) => (
                      <div
                        key={i}
                        className={cn(
                          "w-1 h-1 rounded-full",
                          Math.floor(currentSuggestionIndex / 4) === i
                            ? "bg-primary"
                            : "bg-muted-foreground/20"
                        )}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
