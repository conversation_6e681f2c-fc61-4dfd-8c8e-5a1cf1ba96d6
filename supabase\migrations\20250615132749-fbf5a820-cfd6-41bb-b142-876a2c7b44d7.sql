
-- First, let's check if we have any students data
SELECT COUNT(*) as student_count FROM students;
SELECT COUNT(*) as course_count FROM courses;
SELECT COUNT(*) as enrollment_count FROM enrollments;

-- If the tables are empty, let's insert the sample data from the existing migration
-- This is the same data from supabase/migrations/20250422_insert_sample_data.sql

-- Sample Departments (if not exists)
INSERT INTO departments (name) 
SELECT 'Computer Science' WHERE NOT EXISTS (SELECT 1 FROM departments WHERE name = 'Computer Science');
INSERT INTO departments (name) 
SELECT 'Mathematics' WHERE NOT EXISTS (SELECT 1 FROM departments WHERE name = 'Mathematics');
INSERT INTO departments (name) 
SELECT 'Physics' WHERE NOT EXISTS (SELECT 1 FROM departments WHERE name = 'Physics');
INSERT INTO departments (name) 
SELECT 'Business Administration' WHERE NOT EXISTS (SELECT 1 FROM departments WHERE name = 'Business Administration');
INSERT INTO departments (name) 
SELECT 'English Literature' WHERE NOT EXISTS (SELECT 1 FROM departments WHERE name = 'English Literature');

-- Sample Employees (if not exists)
INSERT INTO employees (name, email, role, department_id, salary)
SELECT 'John <PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Computer Science'), 75000
WHERE NOT EXISTS (SELECT 1 FROM employees WHERE email = '<EMAIL>');

INSERT INTO employees (name, email, role, department_id, salary)
SELECT 'Mary Johnson', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Mathematics'), 72000
WHERE NOT EXISTS (SELECT 1 FROM employees WHERE email = '<EMAIL>');

INSERT INTO employees (name, email, role, department_id, salary)
SELECT 'Robert Davis', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Physics'), 74000
WHERE NOT EXISTS (SELECT 1 FROM employees WHERE email = '<EMAIL>');

-- Sample Students (if not exists)
INSERT INTO students (name, email, dob, address, contact)
SELECT 'Alex Thompson', '<EMAIL>', '2000-05-12', '123 Main St, Anytown, CA', '555-123-4567'
WHERE NOT EXISTS (SELECT 1 FROM students WHERE email = '<EMAIL>');

INSERT INTO students (name, email, dob, address, contact)
SELECT 'Emma Rodriguez', '<EMAIL>', '2001-02-23', '456 Oak Ave, Somewhere, NY', '555-234-5678'
WHERE NOT EXISTS (SELECT 1 FROM students WHERE email = '<EMAIL>');

INSERT INTO students (name, email, dob, address, contact)
SELECT 'Ryan Jackson', '<EMAIL>', '1999-11-08', '789 Pine Rd, Nowhere, TX', '************'
WHERE NOT EXISTS (SELECT 1 FROM students WHERE email = '<EMAIL>');

INSERT INTO students (name, email, dob, address, contact)
SELECT 'Olivia Kim', '<EMAIL>', '2002-07-15', '101 Maple Dr, Anywhere, FL', '************'
WHERE NOT EXISTS (SELECT 1 FROM students WHERE email = '<EMAIL>');

INSERT INTO students (name, email, dob, address, contact)
SELECT 'Ethan Patel', '<EMAIL>', '2000-09-30', '202 Cedar Ln, Everywhere, WA', '************'
WHERE NOT EXISTS (SELECT 1 FROM students WHERE email = '<EMAIL>');

-- Sample Courses (if not exists)
INSERT INTO courses (title, description, duration_months, start_date, end_date, department_id, active)
SELECT 'Introduction to Programming', 'Learn the basics of programming with Python', 3, '2024-05-01', '2024-08-01', (SELECT id FROM departments WHERE name = 'Computer Science'), true
WHERE NOT EXISTS (SELECT 1 FROM courses WHERE title = 'Introduction to Programming');

INSERT INTO courses (title, description, duration_months, start_date, end_date, department_id, active)
SELECT 'Data Structures and Algorithms', 'Advanced programming concepts', 4, '2024-06-01', '2024-10-01', (SELECT id FROM departments WHERE name = 'Computer Science'), true
WHERE NOT EXISTS (SELECT 1 FROM courses WHERE title = 'Data Structures and Algorithms');

INSERT INTO courses (title, description, duration_months, start_date, end_date, department_id, active)
SELECT 'Calculus I', 'Introduction to differential calculus', 4, '2024-05-01', '2024-09-01', (SELECT id FROM departments WHERE name = 'Mathematics'), true
WHERE NOT EXISTS (SELECT 1 FROM courses WHERE title = 'Calculus I');

-- Create some enrollments (if not exists)
DO $$
DECLARE
    student_record RECORD;
    course_record RECORD;
    counter INTEGER := 0;
BEGIN
    -- Only run if enrollments table is empty
    IF (SELECT COUNT(*) FROM enrollments) = 0 THEN
        FOR student_record IN SELECT id FROM students LIMIT 5
        LOOP
            counter := 0;
            FOR course_record IN SELECT id FROM courses LIMIT 3
            LOOP
                -- Enroll each student in 2-3 courses with some randomness
                IF counter < 2 OR (counter = 2 AND RANDOM() > 0.5) THEN
                    INSERT INTO enrollments (student_id, course_id)
                    VALUES (student_record.id, course_record.id);
                END IF;
                counter := counter + 1;
            END LOOP;
        END LOOP;
    END IF;
END $$;

-- Add some sample payments
DO $$
DECLARE
    enrollment_record RECORD;
    payment_status TEXT;
    payment_amount NUMERIC;
BEGIN
    -- Only add payments if payments table is empty
    IF (SELECT COUNT(*) FROM payments) = 0 THEN
        FOR enrollment_record IN SELECT student_id, course_id FROM enrollments LIMIT 10
        LOOP
            -- Set payment status and amount
            IF RANDOM() < 0.6 THEN
                payment_status := 'paid';
                payment_amount := (FLOOR(RANDOM() * 500) + 500)::NUMERIC;
            ELSIF RANDOM() < 0.8 THEN
                payment_status := 'partial';
                payment_amount := (FLOOR(RANDOM() * 300) + 200)::NUMERIC;
            ELSE
                payment_status := 'pending';
                payment_amount := (FLOOR(RANDOM() * 500) + 500)::NUMERIC;
            END IF;
            
            INSERT INTO payments (
                student_id,
                course_id,
                amount,
                method,
                status,
                paid_on
            ) VALUES (
                enrollment_record.student_id,
                enrollment_record.course_id,
                payment_amount,
                'credit_card',
                payment_status,
                CASE 
                    WHEN payment_status = 'paid' THEN CURRENT_TIMESTAMP - (FLOOR(RANDOM() * 30) || ' days')::INTERVAL
                    WHEN payment_status = 'partial' THEN CURRENT_TIMESTAMP - (FLOOR(RANDOM() * 20) || ' days')::INTERVAL
                    ELSE NULL
                END
            );
        END LOOP;
    END IF;
END $$;
