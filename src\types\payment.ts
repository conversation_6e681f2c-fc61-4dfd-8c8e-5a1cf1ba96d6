
export type Payment = {
  id: string;
  student_id: string;
  course_id: string | null;
  amount: number;
  method: 'credit_card' | 'bank_transfer' | 'cash' | 'other';
  status: 'pending' | 'partial' | 'paid' | 'overdue';
  transaction_ref: string | null;
  paid_on: string | null;
  created_at: string;
  student?: {
    name: string;
    email: string;
    address?: string;
    contact?: string;
  };
  course?: {
    title: string;
    description?: string;
  } | null;
};

export type PaymentMethod = 'credit_card' | 'bank_transfer' | 'cash' | 'other';
export type PaymentStatus = 'pending' | 'partial' | 'paid' | 'overdue';

export type PaymentFilters = {
  studentId?: string;
  courseId?: string;
};

export type CreatePaymentData = Omit<Payment, 'id' | 'created_at' | 'student' | 'course'>;
export type UpdatePaymentData = Partial<Omit<Payment, 'id' | 'created_at' | 'student' | 'course'>>;
