
import { Search, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";

interface EmployeeSearchBarProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;

  roleFilter: string[];
  onRoleFilterChange: (roles: string[]) => void;

  departmentFilter: string[];
  onDepartmentFilterChange: (depts: string[]) => void;

  teachingCourseFilter: string[];
  onTeachingCourseFilterChange: (courses: string[]) => void;

  availableRoles: string[];
  availableDepartments: string[];
  availableTeachingCourses: string[];
}

export function EmployeeSearchBar({
  searchQuery,
  onSearchChange,
  roleFilter,
  onRoleFilterChange,
  departmentFilter,
  onDepartmentFilterChange,
  teachingCourseFilter,
  onTeachingCourseFilterChange,
  availableRoles,
  availableDepartments,
  availableTeachingCourses,
}: EmployeeSearchBarProps) {
  const handleRoleChange = (role: string, checked: boolean) => {
    if (checked) {
      onRoleFilterChange([...roleFilter, role]);
    } else {
      onRoleFilterChange(roleFilter.filter(r => r !== role));
    }
  };

  const handleDepartmentChange = (dept: string, checked: boolean) => {
    if (checked) {
      onDepartmentFilterChange([...departmentFilter, dept]);
    } else {
      onDepartmentFilterChange(departmentFilter.filter(d => d !== dept));
    }
  };

  const handleCourseChange = (course: string, checked: boolean) => {
    if (checked) {
      onTeachingCourseFilterChange([...teachingCourseFilter, course]);
    } else {
      onTeachingCourseFilterChange(teachingCourseFilter.filter(c => c !== course));
    }
  };

  const clearAllFilters = () => {
    onRoleFilterChange([]);
    onDepartmentFilterChange([]);
    onTeachingCourseFilterChange([]);
  };

  const hasActiveFilters =
    roleFilter.length > 0 ||
    departmentFilter.length > 0 ||
    teachingCourseFilter.length > 0;

  return (
    <div className="flex items-center gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search employees..."
          className="pl-9"
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
        />
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className={hasActiveFilters ? "bg-primary text-primary-foreground" : ""}
          >
            <Filter className="h-4 w-4" />
            <span className="sr-only">Filter</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <div className="px-2 py-1.5 text-sm font-medium">Role</div>
          {availableRoles.map(role => (
            <DropdownMenuCheckboxItem
              key={role}
              checked={roleFilter.includes(role)}
              onCheckedChange={checked => handleRoleChange(role, checked)}
            >
              {role.charAt(0).toUpperCase() + role.slice(1)}
            </DropdownMenuCheckboxItem>
          ))}
          <DropdownMenuSeparator />

          <div className="px-2 py-1.5 text-sm font-medium">Department</div>
          {availableDepartments.map(dept => (
            <DropdownMenuCheckboxItem
              key={dept}
              checked={departmentFilter.includes(dept)}
              onCheckedChange={checked => handleDepartmentChange(dept, checked)}
            >
              {dept}
            </DropdownMenuCheckboxItem>
          ))}
          <DropdownMenuSeparator />

          <div className="px-2 py-1.5 text-sm font-medium">Courses Teaching</div>
          {availableTeachingCourses.map(course => (
            <DropdownMenuCheckboxItem
              key={course}
              checked={teachingCourseFilter.includes(course)}
              onCheckedChange={checked => handleCourseChange(course, checked)}
            >
              {course}
            </DropdownMenuCheckboxItem>
          ))}

          {hasActiveFilters && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={clearAllFilters}>
                Clear all filters
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
