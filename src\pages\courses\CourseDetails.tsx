
import { useParams, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { useCourseDetails } from "@/hooks/useCourseDetails";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Pencil, Users } from "lucide-react";
import { TeacherAssignment } from "@/components/courses/TeacherAssignment";
import { CourseInfo } from "@/components/courses/CourseInfo";
import { StudentsPreview } from "@/components/courses/StudentsPreview";

export default function CourseDetails() {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  const {
    courseDetails,
    loading,
    fetchCourseDetails,
  } = useCourseDetails();

  // Use instructor_id and instructor from courseDetails
  const currentTeacherId = courseDetails?.instructor_id || null;
  const currentTeacherName = courseDetails?.instructor || "Unassigned";

  useEffect(() => {
    const loadData = async () => {
      if (courseId) {
        setIsLoading(true);
        await fetchCourseDetails(courseId);
        setIsLoading(false);
      }
    };

    loadData();
  }, [courseId, fetchCourseDetails]);

  const handleTeacherUpdated = async () => {
    if (courseId) {
      await fetchCourseDetails(courseId);
    }
  };

  if (isLoading || loading || !courseDetails) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Loading course details...</div>
        </div>
      </div>
    );
  }

  const handleEditClick = () => {
    navigate(`/courses/${courseId}/edit`);
  };

  const handleViewStudentsClick = () => {
    navigate(`/courses/${courseId}/students`);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/courses')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Courses
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleEditClick}
        >
          <Pencil className="mr-2 h-4 w-4" />
          Edit Course
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleViewStudentsClick}
        >
          <Users className="mr-2 h-4 w-4" />
          View Students
        </Button>
      </div>

      <TeacherAssignment
        courseId={courseId!}
        currentTeacherId={currentTeacherId}
        currentTeacherName={currentTeacherName}
        onTeacherUpdated={handleTeacherUpdated}
      />

      <CourseInfo courseDetails={courseDetails} />

      <StudentsPreview courseDetails={courseDetails} courseId={courseId!} />
    </div>
  );
}
