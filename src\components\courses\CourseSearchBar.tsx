
import { Search, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";

interface CourseSearchBarProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  statusFilter: string[];
  onStatusFilterChange: (statuses: string[]) => void;
  departmentFilter: string[];
  onDepartmentFilterChange: (departments: string[]) => void;
  availableDepartments: string[];
}

export function CourseSearchBar({ 
  searchQuery, 
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  departmentFilter,
  onDepartmentFilterChange,
  availableDepartments
}: CourseSearchBarProps) {
  const statusOptions = ['active', 'inactive', 'draft'];

  const handleStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      onStatusFilterChange([...statusFilter, status]);
    } else {
      onStatusFilterChange(statusFilter.filter(s => s !== status));
    }
  };

  const handleDepartmentChange = (department: string, checked: boolean) => {
    if (checked) {
      onDepartmentFilterChange([...departmentFilter, department]);
    } else {
      onDepartmentFilterChange(departmentFilter.filter(d => d !== department));
    }
  };

  const clearAllFilters = () => {
    onStatusFilterChange([]);
    onDepartmentFilterChange([]);
  };

  const hasActiveFilters = statusFilter.length > 0 || departmentFilter.length > 0;

  return (
    <div className="flex items-center gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search courses..."
          className="pl-9"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="icon" className={hasActiveFilters ? "bg-primary text-primary-foreground" : ""}>
            <Filter className="h-4 w-4" />
            <span className="sr-only">Filter</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <div className="px-2 py-1.5 text-sm font-medium">Status</div>
          {statusOptions.map((status) => (
            <DropdownMenuCheckboxItem
              key={status}
              checked={statusFilter.includes(status)}
              onCheckedChange={(checked) => handleStatusChange(status, checked)}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </DropdownMenuCheckboxItem>
          ))}
          
          <DropdownMenuSeparator />
          
          <div className="px-2 py-1.5 text-sm font-medium">Department</div>
          {availableDepartments.map((department) => (
            <DropdownMenuCheckboxItem
              key={department}
              checked={departmentFilter.includes(department)}
              onCheckedChange={(checked) => handleDepartmentChange(department, checked)}
            >
              {department}
            </DropdownMenuCheckboxItem>
          ))}
          
          {hasActiveFilters && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={clearAllFilters}>
                Clear all filters
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
