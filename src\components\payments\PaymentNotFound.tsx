
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";

interface PaymentNotFoundProps {
  paymentId: string;
}

export function PaymentNotFound({ paymentId }: PaymentNotFoundProps) {
  const navigate = useNavigate();

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center gap-4 mb-6">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => navigate('/payments')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Payments
        </Button>
      </div>
      
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-lg text-muted-foreground">Payment not found</p>
          <p className="text-sm text-muted-foreground mt-2">
            The payment with ID "{paymentId}" does not exist or you don't have permission to view it.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
