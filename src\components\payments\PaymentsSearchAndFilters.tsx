
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Filter, Download } from "lucide-react";
import { Payment } from "@/hooks/usePayments";

interface PaymentsSearchAndFiltersProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  statusFilter: string[];
  onStatusFilterChange: (status: string[]) => void;
  methodFilter: string[];
  onMethodFilterChange: (method: string[]) => void;
  studentFilter: string;
  onStudentFilterChange: (student: string) => void;
  courseFilter: string;
  onCourseFilterChange: (course: string) => void;
  payments?: Payment[];
}

export function PaymentsSearchAndFilters({ 
  searchQuery, 
  onSearchChange,
  payments = []
}: PaymentsSearchAndFiltersProps) {
  
  const downloadPaymentsData = () => {
    if (payments.length === 0) {
      return;
    }

    const csvHeaders = [
      'Payment ID',
      'Student Name',
      'Student Email',
      'Course',
      'Amount',
      'Status',
      'Payment Method',
      'Transaction Reference',
      'Payment Date',
      'Created Date'
    ];

    const csvData = payments.map(payment => [
      payment.id,
      payment.student?.name || 'N/A',
      payment.student?.email || 'N/A',
      payment.course?.title || 'General Payment',
      payment.amount.toFixed(2),
      payment.status,
      payment.method.replace('_', ' '),
      payment.transaction_ref || 'N/A',
      payment.paid_on ? new Date(payment.paid_on).toLocaleDateString() : 'N/A',
      new Date(payment.created_at).toLocaleDateString()
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `payments-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="flex items-center gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search payments..."
          className="pl-9"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
      <Button variant="outline" size="icon">
        <Filter className="h-4 w-4" />
        <span className="sr-only">Filter</span>
      </Button>
      <Button 
        variant="outline" 
        size="icon"
        onClick={downloadPaymentsData}
        disabled={payments.length === 0}
      >
        <Download className="h-4 w-4" />
        <span className="sr-only">Download</span>
      </Button>
    </div>
  );
}
