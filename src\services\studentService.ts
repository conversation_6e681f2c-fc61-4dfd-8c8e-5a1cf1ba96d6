
import { supabase } from "@/integrations/supabase/client";
import { determinePaymentStatus } from "@/utils/paymentUtils";
import { validatePaymentMethod, validatePaymentStatus } from "@/utils/paymentValidation";
import { Payment } from "@/hooks/usePayments";
import { StudentDetails } from "@/types/student";

export const fetchStudentDetailsFromApi = async (studentId: string) => {
  const { data: studentData, error: studentError } = await supabase
    .from('students')
    .select(`
      *,
      enrollments(
        course:courses(*)
      ),
      payments(*)
    `)
    .eq('id', studentId)
    .single();

  if (studentError) throw studentError;

  return transformStudentData(studentData);
};

export const transformStudentData = (studentData: any): StudentDetails => {
  const transformedPayments: Payment[] = studentData.payments.map((payment: any) => ({
    ...payment,
    method: validatePaymentMethod(payment.method),
    status: validatePaymentStatus(payment.status)
  }));

  return {
    ...studentData,
    courses: studentData.enrollments.map((enrollment: any) => enrollment.course),
    payments: transformedPayments,
    coursesEnrolled: studentData.enrollments.map((enrollment: any) => enrollment.course.title),
    paymentStatus: determinePaymentStatus(transformedPayments)
  };
};

export const enrollStudentInCourseApi = async (studentId: string, courseId: string) => {
  const { data: existingEnrollment, error: checkError } = await supabase
    .from('enrollments')
    .select('id')
    .eq('student_id', studentId)
    .eq('course_id', courseId)
    .single();

  if (checkError && checkError.code !== 'PGRST116') throw checkError;
  if (existingEnrollment) return null;

  const { data, error } = await supabase
    .from('enrollments')
    .insert({ student_id: studentId, course_id: courseId })
    .select()
    .single();

  if (error) throw error;
  return data;
};

export const updatePaymentStatusApi = async (paymentId: string, status: 'pending' | 'paid' | 'overdue') => {
  const { data, error } = await supabase
    .from('payments')
    .update({ status })
    .eq('id', paymentId)
    .select()
    .single();

  if (error) throw error;
  return data;
};
