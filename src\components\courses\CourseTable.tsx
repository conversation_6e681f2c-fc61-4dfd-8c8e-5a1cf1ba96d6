
import { useNavigate } from "react-router-dom";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Course } from "@/hooks/useCourses";

interface CourseTableProps {
  courses: Course[];
  getStatusColor: (active: boolean | null) => string;
  isLoading: boolean;
}

export function CourseTable({ courses, getStatusColor, isLoading }: CourseTableProps) {
  const navigate = useNavigate();

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader className="bg-muted/50">
          <TableRow className="hover:bg-transparent">
            <TableHead className="font-semibold">Course Name</TableHead>
            <TableHead className="font-semibold">Instructor</TableHead>
            <TableHead className="font-semibold">Duration</TableHead>
            <TableHead className="font-semibold">Enrollment</TableHead>
            <TableHead className="font-semibold">Status</TableHead>
            <TableHead className="w-14 font-semibold"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                Loading courses...
              </TableCell>
            </TableRow>
          ) : courses.length > 0 ? (
            courses.map((course) => (
              <TableRow 
                key={course.id} 
                className="cursor-pointer" 
                onClick={() => navigate(`/courses/${course.id}`)}
              >
                <TableCell className="font-medium">
                  <div>
                    <div>{course.title}</div>
                    <div className="text-sm text-muted-foreground truncate max-w-xs">
                      {course.description}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{course.instructor || 'Unassigned'}</TableCell>
                <TableCell>{course.duration_months} months</TableCell>
                <TableCell>{course.enrollment_count || 0} enrolled</TableCell>
                <TableCell>
                  <Badge variant="outline" className={getStatusColor(course.active)}>
                    {course.active ? 'Active' : course.active === false ? 'Inactive' : 'Draft'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/courses/${course.id}`);
                      }}>
                        View details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/courses/${course.id}/edit`);
                      }}>
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/courses/${course.id}/students`);
                      }}>
                        View students
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                No courses found matching your search criteria.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
