import { createClient } from "https://esm.sh/@supabase/supabase-js@2.50.0";

const SUPABASE_URL = "https://zpgyikcpoaioldomuutp.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Field type mappings
const FIELD_TYPES = {
  students: {
    name: 'text',
    email: 'text', 
    dob: 'date',
    address: 'text',
    contact: 'text',
  },
  courses: {
    title: 'text',
    description: 'text',
    duration_months: 'number',
    start_date: 'date',
    end_date: 'date',
    active: 'boolean',
  },
  payments: {
    amount: 'number',
    method: 'text',
    status: 'text',
    paid_on: 'date',
  },
  enrollments: {
    enrolled_on: 'date',
  },
  employees: {
    name: 'text',
    email: 'text',
    role: 'text',
    salary: 'number',
    salary_paid: 'number',
  },
  departments: {
    name: 'text',
  }
};

// Build dynamic query based on conditions
function buildDynamicQuery(supabase: any, entity: string, conditions: any[]) {
  console.log(`Building dynamic query for entity: ${entity} with conditions:`, conditions);
  
  let query = supabase.from(entity).select('*');
  
  conditions.forEach((condition: any) => {
    const { field, operator, value } = condition;
    const fieldType = FIELD_TYPES[entity as keyof typeof FIELD_TYPES]?.[field as keyof typeof FIELD_TYPES[keyof typeof FIELD_TYPES]];
    
    console.log(`Processing condition: ${field} ${operator} ${value} (type: ${fieldType})`);
    
    switch (operator) {
      case 'equals':
        if (fieldType === 'boolean') {
          query = query.eq(field, value === 'true');
        } else if (fieldType === 'number') {
          query = query.eq(field, parseFloat(value));
        } else {
          query = query.eq(field, value);
        }
        break;
        
      case 'not_equals':
        if (fieldType === 'boolean') {
          query = query.neq(field, value === 'true');
        } else if (fieldType === 'number') {
          query = query.neq(field, parseFloat(value));
        } else {
          query = query.neq(field, value);
        }
        break;
        
      case 'contains':
        query = query.ilike(field, `%${value}%`);
        break;
        
      case 'not_contains':
        query = query.not('like', `%${value}%`);
        break;
        
      case 'starts_with':
        query = query.ilike(field, `${value}%`);
        break;
        
      case 'ends_with':
        query = query.ilike(field, `%${value}`);
        break;
        
      case 'is_empty':
        query = query.eq(field, '');
        break;
        
      case 'is_not_empty':
        query = query.neq(field, '');
        break;
        
      case 'is_null':
        query = query.is(field, null);
        break;
        
      case 'is_not_null':
        query = query.not('is', null);
        break;
        
      case 'greater_than':
        if (fieldType === 'number') {
          query = query.gt(field, parseFloat(value));
        } else if (fieldType === 'date') {
          query = query.gt(field, value);
        }
        break;
        
      case 'greater_than_equal':
        if (fieldType === 'number') {
          query = query.gte(field, parseFloat(value));
        } else if (fieldType === 'date') {
          query = query.gte(field, value);
        }
        break;
        
      case 'less_than':
        if (fieldType === 'number') {
          query = query.lt(field, parseFloat(value));
        } else if (fieldType === 'date') {
          query = query.lt(field, value);
        }
        break;
        
      case 'less_than_equal':
        if (fieldType === 'number') {
          query = query.lte(field, parseFloat(value));
        } else if (fieldType === 'date') {
          query = query.lte(field, value);
        }
        break;
        
      case 'between':
        const [min, max] = value.split(',').map((v: string) => v.trim());
        if (fieldType === 'number') {
          query = query.gte(field, parseFloat(min)).lte(field, parseFloat(max));
        } else if (fieldType === 'date') {
          query = query.gte(field, min).lte(field, max);
        }
        break;
        
      case 'before':
        query = query.lt(field, value);
        break;
        
      case 'after':
        query = query.gt(field, value);
        break;
        
      case 'on_or_before':
        query = query.lte(field, value);
        break;
        
      case 'on_or_after':
        query = query.gte(field, value);
        break;
        
      case 'is_true':
        query = query.eq(field, true);
        break;
        
      case 'is_false':
        query = query.eq(field, false);
        break;
        
      default:
        console.warn(`Unknown operator: ${operator}`);
    }
  });
  
  return query;
}

// Prebuilt queries that use the Supabase client methods directly
const PREBUILT_QUERY_HANDLERS = {
  'students-enrolled-data-analysis': async (supabase: any) => {
    console.log("Executing students-enrolled-data-analysis query");
    const { data, error } = await supabase
      .from('students')
      .select(`
        name, 
        email,
        enrollments(
          courses(title)
        )
      `);
    
    if (error) {
      console.error("Error in students-enrolled-data-analysis:", error);
      throw error;
    }
    
    return data.filter((student: any) => 
      student.enrollments.some((enrollment: any) => 
        enrollment.courses?.title === 'Data Analysis with Python'
      )
    ).map((student: any) => ({
      name: student.name,
      email: student.email
    }));
  },

  'students-max-fees': async (supabase: any) => {
    console.log("Executing students-max-fees query");
    const { data, error } = await supabase
      .from('payments')
      .select(`
        amount,
        students(name, email)
      `);
    
    if (error) {
      console.error("Error in students-max-fees:", error);
      throw error;
    }
    
    // Group by student and sum amounts
    const studentTotals = data.reduce((acc: any, payment: any) => {
      const student = payment.students;
      if (student) {
        const key = `${student.name}-${student.email}`;
        if (!acc[key]) {
          acc[key] = { name: student.name, email: student.email, total_paid: 0 };
        }
        acc[key].total_paid += payment.amount || 0;
      }
      return acc;
    }, {});
    
    const sorted = Object.values(studentTotals).sort((a: any, b: any) => b.total_paid - a.total_paid);
    return sorted.length > 0 ? [sorted[0]] : [];
  },

  'students-not-enrolled': async (supabase: any) => {
    console.log("Executing students-not-enrolled query");
    const { data: students, error: studentsError } = await supabase
      .from('students')
      .select('id, name, email');
    
    if (studentsError) {
      console.error("Error fetching students:", studentsError);
      throw studentsError;
    }
    
    const { data: enrollments, error: enrollmentsError } = await supabase
      .from('enrollments')
      .select('student_id');
    
    if (enrollmentsError) {
      console.error("Error fetching enrollments:", enrollmentsError);
      throw enrollmentsError;
    }
    
    const enrolledStudentIds = new Set(enrollments.map((e: any) => e.student_id));
    
    return students.filter((student: any) => !enrolledStudentIds.has(student.id))
      .map((student: any) => ({ name: student.name, email: student.email }));
  },

  'students-name-n-fullstack': async (supabase: any) => {
    console.log("Executing students-name-n-fullstack query");
    const { data, error } = await supabase
      .from('students')
      .select(`
        name, 
        email,
        enrollments(
          courses(title)
        )
      `)
      .ilike('name', 'N%');
    
    if (error) {
      console.error("Error in students-name-n-fullstack:", error);
      throw error;
    }
    
    return data.filter((student: any) => 
      student.enrollments.some((enrollment: any) => 
        enrollment.courses?.title === 'Full Stack Development'
      )
    ).map((student: any) => ({
      name: student.name,
      email: student.email
    }));
  },

  'students-pending-fees-contact': async (supabase: any) => {
    console.log("Executing students-pending-fees-contact query");
    const { data, error } = await supabase
      .from('payments')
      .select(`
        students(name, contact, email)
      `)
      .eq('status', 'pending');
    
    if (error) {
      console.error("Error in students-pending-fees-contact:", error);
      throw error;
    }
    
    const uniqueStudents = new Map();
    data.forEach((payment: any) => {
      if (payment.students) {
        const key = payment.students.email;
        uniqueStudents.set(key, payment.students);
      }
    });
    
    return Array.from(uniqueStudents.values());
  },

  'highest-paid-employee': async (supabase: any) => {
    console.log("Executing highest-paid-employee query");
    const { data, error } = await supabase
      .from('employees')
      .select('name, email, salary')
      .order('salary', { ascending: false })
      .limit(1);
    
    if (error) {
      console.error("Error in highest-paid-employee:", error);
      throw error;
    }
    return data;
  },

  'teachers-more-than-2-courses': async (supabase: any) => {
    console.log("Executing teachers-more-than-2-courses query");
    const { data, error } = await supabase
      .from('employees')
      .select(`
        name, 
        email,
        teaches(course_id)
      `)
      .eq('role', 'teacher');
    
    if (error) {
      console.error("Error in teachers-more-than-2-courses:", error);
      throw error;
    }
    
    return data.filter((teacher: any) => teacher.teaches.length > 2)
      .map((teacher: any) => ({
        name: teacher.name,
        email: teacher.email,
        course_count: teacher.teaches.length
      }));
  },

  'courses-duration-more-6-months': async (supabase: any) => {
    console.log("Executing courses-duration-more-6-months query");
    const { data, error } = await supabase
      .from('courses')
      .select('id')
      .gt('duration_months', 6);
    
    if (error) {
      console.error("Error in courses-duration-more-6-months:", error);
      throw error;
    }
    return [{ courses_more_than_6_months: data.length }];
  },

  'department-managers-demo-students': async (supabase: any) => {
    console.log("Executing department-managers-demo-students query");
    const { data, error } = await supabase
      .from('departments')
      .select(`
        name,
        employees!departments_manager_employee_id_fkey(name, email)
      `);
    
    if (error) {
      console.error("Error in department-managers-demo-students:", error);
      throw error;
    }
    
    return data.map((dept: any) => ({
      department_name: dept.name,
      manager_name: dept.employees?.name || 'No manager assigned',
      manager_email: dept.employees?.email || 'N/A'
    }));
  },

  'teachers-highest-students': async (supabase: any) => {
    console.log("Executing teachers-highest-students query");
    const { data, error } = await supabase
      .from('employees')
      .select(`
        name, 
        email,
        teaches(
          course_id,
          courses(
            enrollments(student_id)
          )
        )
      `)
      .eq('role', 'teacher');
    
    if (error) {
      console.error("Error in teachers-highest-students:", error);
      throw error;
    }
    
    const teacherStudentCounts = data.map((teacher: any) => {
      const uniqueStudents = new Set();
      teacher.teaches.forEach((teach: any) => {
        teach.courses?.enrollments?.forEach((enrollment: any) => {
          uniqueStudents.add(enrollment.student_id);
        });
      });
      
      return {
        name: teacher.name,
        email: teacher.email,
        student_count: uniqueStudents.size
      };
    });
    
    const sorted = teacherStudentCounts.sort((a, b) => b.student_count - a.student_count);
    return sorted.length > 0 ? [sorted[0]] : [];
  },

  'employees-salary-10k-frontend': async (supabase: any) => {
    console.log("Executing employees-salary-10k-frontend query");
    const { data, error } = await supabase
      .from('employees')
      .select(`
        name, 
        email, 
        salary,
        departments!employees_department_id_fkey(name)
      `)
      .gt('salary', 10000);
    
    if (error) {
      console.error("Error in employees-salary-10k-frontend:", error);
      throw error;
    }
    
    return data.filter((employee: any) => 
      employee.departments?.name?.toLowerCase().includes('frontend') ||
      employee.departments?.name?.toLowerCase().includes('front end')
    );
  },

  'students-no-teachers': async (supabase: any) => {
    console.log("Executing students-no-teachers query");
    const { data, error } = await supabase
      .from('students')
      .select(`
        name, 
        email,
        enrollments(
          course_id,
          courses(
            teaches(employee_id)
          )
        )
      `);
    
    if (error) {
      console.error("Error in students-no-teachers:", error);
      throw error;
    }
    
    return data.filter((student: any) => 
      student.enrollments.some((enrollment: any) => 
        !enrollment.courses?.teaches || enrollment.courses.teaches.length === 0
      )
    ).map((student: any) => ({
      name: student.name,
      email: student.email
    }));
  },

  'employees-salaries-paid': async (supabase: any) => {
    console.log("Executing employees-salaries-paid query");
    const { data, error } = await supabase
      .from('employees')
      .select('name, email, salary, salary_paid');
    
    if (error) {
      console.error("Error in employees-salaries-paid:", error);
      throw error;
    }
    
    return data.filter((employee: any) => 
      (employee.salary_paid || 0) >= (employee.salary || 0)
    );
  },

  'students-under-20-java': async (supabase: any) => {
    console.log("Executing students-under-20-java query");
    const { data, error } = await supabase
      .from('students')
      .select(`
        name, 
        email, 
        dob,
        enrollments(
          courses(title)
        )
      `);
    
    if (error) {
      console.error("Error in students-under-20-java:", error);
      throw error;
    }
    
    const currentDate = new Date();
    
    return data.filter((student: any) => {
      // Check age
      if (!student.dob) return false;
      const birthDate = new Date(student.dob);
      const age = currentDate.getFullYear() - birthDate.getFullYear();
      const monthDiff = currentDate.getMonth() - birthDate.getMonth();
      const actualAge = (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) 
        ? age - 1 : age;
      
      // Check if under 20 and enrolled in Java course
      return actualAge < 20 && student.enrollments.some((enrollment: any) => 
        enrollment.courses?.title?.toLowerCase().includes('java')
      );
    }).map((student: any) => ({
      name: student.name,
      email: student.email,
      dob: student.dob
    }));
  }
};

Deno.serve(async (req) => {
  console.log("Advanced filter function called with method:", req.method);
  console.log("Request URL:", req.url);
  console.log("Environment check - SUPABASE_SERVICE_ROLE_KEY exists:", !!SUPABASE_SERVICE_ROLE_KEY);
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log("Handling CORS preflight request");
    return new Response(null, { headers: corsHeaders });
  }
  
  try {
    if (!SUPABASE_SERVICE_ROLE_KEY) {
      console.error("Missing SUPABASE_SERVICE_ROLE_KEY environment variable");
      return new Response(
        JSON.stringify({ 
          error: "Server configuration error: Missing service role key",
          details: "Please configure SUPABASE_SERVICE_ROLE_KEY in Supabase Edge Functions secrets"
        }),
        { 
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log("Creating Supabase client");
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    let requestBody;
    try {
      requestBody = await req.json();
      console.log("Request body received:", JSON.stringify(requestBody, null, 2));
    } catch (parseError) {
      console.error("Failed to parse request body:", parseError);
      return new Response(
        JSON.stringify({ error: "Invalid JSON in request body" }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
    
    const { prebuiltQuery, getAllData, entity, conditions } = requestBody;
    
    // Handle get all data request
    if (getAllData && entity) {
      console.log("Processing getAllData request for entity:", entity);
      const { data, error } = await supabase.from(entity).select('*');
      
      if (error) {
        console.error("Error getting all data for entity", entity, ":", error);
        throw error;
      }
      
      console.log(`Successfully retrieved ${data.length} records for entity: ${entity}`);
      return new Response(
        JSON.stringify({ results: data }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    // Handle prebuilt query
    if (prebuiltQuery) {
      console.log("Processing prebuilt query:", prebuiltQuery);
      
      const queryHandler = PREBUILT_QUERY_HANDLERS[prebuiltQuery as keyof typeof PREBUILT_QUERY_HANDLERS];
      
      if (!queryHandler) {
        console.error("Query handler not found for:", prebuiltQuery);
        return new Response(
          JSON.stringify({ error: `Query '${prebuiltQuery}' not found` }),
          { 
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }
      
      try {
        console.log("Executing query handler for:", prebuiltQuery);
        const results = await queryHandler(supabase);
        
        console.log(`Query '${prebuiltQuery}' completed successfully. Results count:`, results.length);
        return new Response(
          JSON.stringify({ results }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      } catch (queryError) {
        console.error("Query execution error for", prebuiltQuery, ":", queryError);
        return new Response(
          JSON.stringify({ 
            error: `Query execution failed: ${queryError.message}`,
            details: queryError.toString()
          }),
          { 
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }
    }
    
    // Handle advanced filters (conditions)
    if (conditions && Array.isArray(conditions) && entity) {
      console.log("Processing advanced filters with conditions:", conditions.length);
      
      try {
        const query = buildDynamicQuery(supabase, entity, conditions);
        const { data, error } = await query;
        
        if (error) {
          console.error("Error executing dynamic query:", error);
          throw error;
        }
        
        console.log(`Dynamic query completed successfully. Results count:`, data.length);
        return new Response(
          JSON.stringify({ results: data }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      } catch (queryError) {
        console.error("Dynamic query execution error:", queryError);
        return new Response(
          JSON.stringify({ 
            error: `Dynamic query execution failed: ${queryError.message}`,
            details: queryError.toString()
          }),
          { 
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }
    }
    
    console.log("Invalid request - no valid parameters provided");
    return new Response(
      JSON.stringify({ error: "Invalid request parameters. Please provide either 'prebuiltQuery', 'getAllData', or 'conditions' with 'entity'." }),
      { 
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
    
  } catch (error) {
    console.error("Unexpected error processing request:", error);
    return new Response(
      JSON.stringify({ 
        error: `Internal server error: ${error.message}`,
        details: error.toString()
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
