
import { Outlet } from "react-router-dom";
import { Sidebar } from "./Sidebar";
import { Header } from "./Header";
import { useSidebar } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

export function DashboardLayout() {
  const { collapsed } = useSidebar();
  const isMobile = useIsMobile();
  
  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className={cn(
        "flex flex-col flex-1 transition-all duration-300",
        isMobile ? "ml-0" : (collapsed ? "ml-16" : "ml-64")
      )}>
        <Header />
        <main className="flex-1 overflow-y-auto p-3 md:p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
}
