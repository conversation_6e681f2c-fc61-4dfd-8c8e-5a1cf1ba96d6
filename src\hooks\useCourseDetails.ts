
import { useState, useCallback } from 'react';
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Course } from "@/hooks/useCourses";

export interface CourseDetails extends Course {
  department?: {
    name: string;
  } | null;
  enrollment_count: number;
  students: {
    id: string;
    name: string;
    email: string;
    enrollment_id: string;
    enrolled_on: string;
  }[];
  instructor_id?: string | null;
}

export const useCourseDetails = () => {
  const [courseDetails, setCourseDetails] = useState<CourseDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchCourseDetails = useCallback(async (courseId: string) => {
    try {
      setLoading(true);
      setError(null);
      // Get course details with department information
      const { data: course, error: courseError } = await supabase
        .from("courses")
        .select(`
          *,
          department:departments(name),
          teaches(
            employee_id,
            employee:employees(name)
          )
        `)
        .eq("id", courseId)
        .single();

      if (courseError) throw courseError;

      // Get enrollment count
      const { count: enrollmentCount, error: countError } = await supabase
        .from("enrollments")
        .select("*", { count: 'exact', head: true })
        .eq("course_id", courseId);

      if (countError) throw countError;

      // Get students enrolled in course
      const { data: enrollments, error: enrollmentsError } = await supabase
        .from("enrollments")
        .select(`
          id,
          enrolled_on,
          student:students(
            id,
            name,
            email
          )
        `)
        .eq("course_id", courseId);

      if (enrollmentsError) throw enrollmentsError;

      const students = enrollments.map((enrollment: any) => ({
        id: enrollment.student.id,
        name: enrollment.student.name,
        email: enrollment.student.email,
        enrollment_id: enrollment.id,
        enrolled_on: enrollment.enrolled_on
      }));

      // Extract assigned teacher id&name if available
      let instructor_id: string | null = null;
      let instructor: string = "Unassigned";
      if (course.teaches && Array.isArray(course.teaches) && course.teaches.length > 0) {
        instructor_id = course.teaches[0].employee_id || null;
        instructor = course.teaches[0].employee?.name || "Unassigned";
      }

      // Combine all the data
      const detailedCourse: CourseDetails = {
        ...course,
        enrollment_count: enrollmentCount || 0,
        instructor_id,
        instructor,
        students
      };

      setCourseDetails(detailedCourse);
      return detailedCourse;
    } catch (err) {
      const error = err as Error;
      console.error('Error fetching course details:', error);
      setError(error);
      toast({
        title: "Error",
        description: "Failed to fetch course details",
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateCourse = async (courseId: string, data: Partial<Course>) => {
    try {
      setLoading(true);
      
      const { data: updatedCourse, error } = await supabase
        .from("courses")
        .update(data)
        .eq("id", courseId)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Course Updated",
        description: "Course information has been updated successfully"
      });

      await fetchCourseDetails(courseId);
      
      return updatedCourse;
    } catch (err) {
      console.error('Error updating course:', err);
      toast({
        title: "Error",
        description: "Failed to update course information",
        variant: "destructive"
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  const removeStudent = async (enrollmentId: string, courseId: string) => {
    try {
      const { error } = await supabase
        .from("enrollments")
        .delete()
        .eq("id", enrollmentId);

      if (error) throw error;
      
      toast({
        title: "Student Removed",
        description: "Student has been removed from this course"
      });
      
      await fetchCourseDetails(courseId);
      
      return true;
    } catch (err) {
      console.error('Error removing student from course:', err);
      toast({
        title: "Error",
        description: "Failed to remove student from course",
        variant: "destructive"
      });
      return false;
    }
  };

  return {
    courseDetails,
    loading,
    error,
    fetchCourseDetails,
    updateCourse,
    removeStudent
  };
};

