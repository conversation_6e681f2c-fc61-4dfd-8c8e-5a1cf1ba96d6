
-- Drop existing RLS policies that require authentication
DROP POLICY IF EXISTS "Authenticated can view teaches" ON public.teaches;
DROP POLICY IF EXISTS "Authenticated can assign teacher (insert)" ON public.teaches;
DROP POLICY IF EXISTS "Authenticated can unassign teacher (delete)" ON public.teaches;

-- Create new policies that allow public access
CREATE POLICY "Allow public read access to teaches" 
  ON public.teaches 
  FOR SELECT 
  USING (true);

CREATE POLICY "Allow public insert access to teaches" 
  ON public.teaches 
  FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow public delete access to teaches" 
  ON public.teaches 
  FOR DELETE 
  USING (true);

CREATE POLICY "Allow public update access to teaches" 
  ON public.teaches 
  FOR UPDATE 
  USING (true);
