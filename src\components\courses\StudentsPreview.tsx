
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Users } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { CourseDetails } from "@/hooks/useCourseDetails";

interface StudentsPreviewProps {
  courseDetails: CourseDetails;
  courseId: string;
}

export function StudentsPreview({ courseDetails, courseId }: StudentsPreviewProps) {
  const navigate = useNavigate();

  const handleViewStudentsClick = () => {
    navigate(`/courses/${courseId}/students`);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle>Students Enrolled</CardTitle>
        <Button
          variant="outline"
          size="sm"
          onClick={handleViewStudentsClick}
        >
          <Users className="mr-2 h-4 w-4" />
          View All
        </Button>
      </CardHeader>
      <CardContent>
        {courseDetails.students.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            No students enrolled in this course
          </div>
        ) : (
          <div className="space-y-4">
            {courseDetails.students.slice(0, 5).map((student) => (
              <div key={student.id} className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{student.name}</p>
                  <p className="text-sm text-muted-foreground">{student.email}</p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate(`/students/${student.id}`)}
                >
                  View
                </Button>
              </div>
            ))}
            {courseDetails.students.length > 5 && (
              <div className="text-center pt-2">
                <Button
                  variant="link"
                  onClick={handleViewStudentsClick}
                >
                  View {courseDetails.students.length - 5} more students
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
