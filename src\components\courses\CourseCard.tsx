
import { useNavigate } from "react-router-dom";
import { MoreHorizontal } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Course } from "@/hooks/useCourses";

interface CourseCardProps {
  course: Course;
  getStatusColor: (active: boolean | null) => string;
}

export function CourseCard({ course, getStatusColor }: CourseCardProps) {
  const navigate = useNavigate();

  return (
    <Card 
      key={course.id} 
      className="overflow-hidden cursor-pointer" 
      onClick={() => navigate(`/courses/${course.id}`)}
    >
      <div className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold text-lg">{course.title}</h3>
            <p className="text-muted-foreground text-sm mt-1 line-clamp-2">
              {course.description}
            </p>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                navigate(`/courses/${course.id}`);
              }}>
                View details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                navigate(`/courses/${course.id}/edit`);
              }}>
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                navigate(`/courses/${course.id}/students`);
              }}>
                View students
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm">
            <span className="text-muted-foreground">Instructor: </span>
            <span>{course.instructor || 'Unassigned'}</span>
          </div>
          <Badge variant="outline" className={getStatusColor(course.active)}>
            {course.active ? 'Active' : course.active === false ? 'Inactive' : 'Draft'}
          </Badge>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-2">
          <div className="text-sm">
            <span className="text-muted-foreground">Duration: </span>
            <span>{course.duration_months} months</span>
          </div>
          <div className="text-sm">
            <span className="text-muted-foreground">Enrollment: </span>
            <span>{course.enrollment_count || 0} enrolled</span>
          </div>
        </div>
      </div>
    </Card>
  );
}
