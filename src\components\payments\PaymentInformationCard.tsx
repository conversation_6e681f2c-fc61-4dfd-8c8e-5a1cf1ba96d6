
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Payment } from "@/hooks/usePayments";
import { getPaymentStatusColor } from "@/utils/statusColorUtils";

interface PaymentInformationCardProps {
  payment: Payment;
}

export function PaymentInformationCard({ payment }: PaymentInformationCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <Card className="md:col-span-2">
      <CardHeader>
        <CardTitle>Payment Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between border-b pb-2">
          <span className="text-muted-foreground">Amount</span>
          <span className="text-xl font-semibold">${payment.amount.toFixed(2)}</span>
        </div>
        
        <div className="flex items-center justify-between border-b pb-2">
          <span className="text-muted-foreground">Status</span>
          <Badge variant="outline" className={getPaymentStatusColor(payment.status)}>
            {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between border-b pb-2">
          <span className="text-muted-foreground">Payment Method</span>
          <span className="capitalize">{payment.method.replace('_', ' ')}</span>
        </div>
        
        <div className="flex items-center justify-between border-b pb-2">
          <span className="text-muted-foreground">Transaction Reference</span>
          <span>{payment.transaction_ref || 'N/A'}</span>
        </div>
        
        <div className="flex items-center justify-between border-b pb-2">
          <span className="text-muted-foreground">Payment Date</span>
          <span>{payment.paid_on ? formatDate(payment.paid_on) : 'Not paid yet'}</span>
        </div>
        
        <div className="flex items-center justify-between pb-2">
          <span className="text-muted-foreground">Created On</span>
          <span>{formatDate(payment.created_at)}</span>
        </div>
      </CardContent>
    </Card>
  );
}
