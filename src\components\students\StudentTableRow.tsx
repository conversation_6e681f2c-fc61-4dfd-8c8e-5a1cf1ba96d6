
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { StudentWithPayments } from "@/hooks/useStudents";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DeleteStudentDialog } from "./DeleteStudentDialog";

interface StudentTableRowProps {
  student: StudentWithPayments;
  onStudentDeleted: () => void;
}

export function StudentTableRow({ student, onStudentDeleted }: StudentTableRowProps) {
  const navigate = useNavigate();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-success/20 text-success hover:bg-success/30";
      case "pending":
        return "bg-warning/20 text-warning hover:bg-warning/30";
      case "overdue":
        return "bg-danger/20 text-danger hover:bg-danger/30";
      default:
        return "";
    }
  };

  return (
    <>
      <tr
        className={`
          group cursor-pointer transition-colors duration-200 ease-out 
          hover:bg-muted/60 focus-within:bg-muted/50 animate-fade-in
          border-l-4 border-transparent
          hover:border-blue-accent focus-within:border-blue-accent
        `}
        onClick={() => navigate(`/students/${student.id}`)}
        style={{}}
      >
        <td className="p-4">
          <div>
            <div>{student.name}</div>
            <div className="text-sm text-muted-foreground">{student.email}</div>
          </div>
        </td>
        <td className="p-4">{new Date(student.created_at).toLocaleDateString()}</td>
        <td className="p-4">
          <div className="flex flex-wrap gap-1">
            {student.coursesEnrolled.map((course, index) => (
              <Badge key={index} variant="secondary" className="font-normal">
                {course}
              </Badge>
            ))}
          </div>
        </td>
        <td className="p-4">
          <Badge
            variant="outline"
            className={getPaymentStatusColor(student.paymentStatus)}
          >
            {student.paymentStatus.charAt(0).toUpperCase() +
              student.paymentStatus.slice(1)}
          </Badge>
        </td>
        <td className="p-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/students/${student.id}`);
                }}
              >
                View details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/students/${student.id}/edit`);
                }}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/students/${student.id}/courses`);
                }}
              >
                View courses
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  setDeleteDialogOpen(true);
                }}
                className="text-destructive focus:text-destructive"
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </td>
      </tr>
      
      <DeleteStudentDialog
        student={student}
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onStudentDeleted={onStudentDeleted}
      />
    </>
  );
}
