
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useEmployeeDetails } from "@/hooks/useEmployeeDetails";
import { ArrowLeft, Edit, Book } from "lucide-react";

export default function EmployeeDetails() {
  const { employeeId } = useParams();
  const navigate = useNavigate();
  const { employeeDetails, loading, error } = useEmployeeDetails(employeeId);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Loading employee details...</div>
        </div>
      </div>
    );
  }

  if (error || !employeeDetails) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Employee not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => navigate("/employees")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Employees
        </Button>
        <div className="flex gap-2">
          <Button onClick={() => navigate(`/employees/${employeeId}/courses`)}>
            <Book className="mr-2 h-4 w-4" />
            View Courses
          </Button>
          <Button onClick={() => navigate(`/employees/${employeeId}/edit`)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Details
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Employee Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Name</h3>
              <p className="text-lg">{employeeDetails.name}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
              <p className="text-lg">{employeeDetails.email}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Role</h3>
              <Badge variant="outline" className="mt-1">
                {employeeDetails.role}
              </Badge>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Department</h3>
              <p className="text-lg">{employeeDetails.department?.name || "Not assigned"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Salary</h3>
              <p className="text-lg">${employeeDetails.salary?.toLocaleString() || "Not set"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Join Date</h3>
              <p className="text-lg">
                {new Date(employeeDetails.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {employeeDetails.role === "teacher" && (
        <Card>
          <CardHeader>
            <CardTitle>Assigned Courses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {employeeDetails.teaches && employeeDetails.teaches.length > 0 ? (
                employeeDetails.teaches.map((teaching: any) => (
                  <Card key={teaching.course.id} className="cursor-pointer hover:bg-muted/50" onClick={() => navigate(`/courses/${teaching.course.id}`)}>
                    <CardContent className="p-4">
                      <h4 className="font-medium">{teaching.course.title}</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        {teaching.course.description?.substring(0, 100)}
                        {teaching.course.description?.length > 100 ? "..." : ""}
                      </p>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <p className="text-muted-foreground col-span-full">No courses assigned yet.</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
