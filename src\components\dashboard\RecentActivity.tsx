import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { Activity, Clock } from "lucide-react";
import { useState, useEffect } from "react";

type Activity = {
  id: string;
  user: {
    name: string;
    avatar?: string;
    initials: string;
  };
  action: string;
  target: string;
  timestamp: string;
  type: "enrollment" | "payment" | "course" | "user";
};

const activities: Activity[] = [
  {
    id: "1",
    user: {
      name: "<PERSON>",
      initials: "<PERSON><PERSON>",
    },
    action: "enrolled in",
    target: "Web Development Fundamentals",
    timestamp: "2 min ago",
    type: "enrollment",
  },
  {
    id: "2",
    user: {
      name: "<PERSON>",
      initials: "<PERSON>",
    },
    action: "completed payment for",
    target: "Data Science Bootcamp",
    timestamp: "1 hr ago",
    type: "payment",
  },
  {
    id: "3",
    user: {
      name: "<PERSON>",
      initials: "DL",
    },
    action: "finished",
    target: "Python for Beginners",
    timestamp: "3 hrs ago",
    type: "course",
  },
  {
    id: "4",
    user: {
      name: "<PERSON>",
      initials: "SW",
    },
    action: "joined as",
    target: "new student",
    timestamp: "5 hrs ago",
    type: "user",
  },
  {
    id: "5",
    user: {
      name: "<PERSON>",
      initials: "MB",
    },
    action: "scheduled",
    target: "UX Design Workshop",
    timestamp: "Yesterday",
    type: "course",
  },
];

const getActivityStyle = (type: string) => {
  switch (type) {
    case "enrollment": return {
      bg: "from-blue-500/20 to-cyan-500/20",
      border: "border-blue-200/30 dark:border-blue-400/20",
      text: "text-blue-600 dark:text-blue-400",
      hoverBg: "hover:from-blue-500/30 hover:to-cyan-500/30"
    };
    case "payment": return {
      bg: "from-emerald-500/20 to-teal-500/20",
      border: "border-emerald-200/30 dark:border-emerald-400/20",
      text: "text-emerald-600 dark:text-emerald-400",
      hoverBg: "hover:from-emerald-500/30 hover:to-teal-500/30"
    };
    case "course": return {
      bg: "from-purple-500/20 to-indigo-500/20",
      border: "border-purple-200/30 dark:border-purple-400/20",
      text: "text-purple-600 dark:text-purple-400",
      hoverBg: "hover:from-purple-500/30 hover:to-indigo-500/30"
    };
    case "user": return {
      bg: "from-amber-500/20 to-orange-500/20",
      border: "border-amber-200/30 dark:border-amber-400/20",
      text: "text-amber-600 dark:text-amber-400",
      hoverBg: "hover:from-amber-500/30 hover:to-orange-500/30"
    };
    default: return {
      bg: "from-slate-500/20 to-gray-500/20",
      border: "border-slate-200/30 dark:border-slate-400/20",
      text: "text-slate-600 dark:text-slate-400",
      hoverBg: "hover:from-slate-500/30 hover:to-gray-500/30"
    };
  }
};

export function RecentActivity() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 200);
    return () => clearTimeout(timer);
  }, []);

  return (
    <Card className="border-0 shadow-none bg-transparent">
      <CardHeader className="pb-6">
        <div className="flex items-center gap-3">
          <div className="backdrop-blur-sm bg-purple-500/20 p-2 rounded-lg border border-purple-200/30 transform transition-all duration-300 hover:scale-110 hover:rotate-6">
            <Activity className="w-4 h-4 text-purple-600 dark:text-purple-400 transform transition-transform duration-300 hover:animate-pulse" />
          </div>
          <div>
            <CardTitle className="text-xl font-black text-slate-900 dark:text-white">Recent Activity</CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-400 font-medium">
              Latest updates from your platform
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Set overflow-visible to prevent clipping */}
        <div className="relative h-[400px] pr-4 overflow-visible">
          <ScrollArea className="h-full overflow-visible">
            <div className="space-y-4">
              {activities.map((activity, index) => {
                const style = getActivityStyle(activity.type);
                return (
                  <div
                    key={activity.id}
                    className={cn(
                      "backdrop-blur-xl bg-gradient-to-r border rounded-2xl p-4 hover:scale-105 hover:-translate-y-1 transition-all duration-500 group cursor-pointer relative overflow-visible transform-gpu",
                      style.bg,
                      style.hoverBg,
                      style.border,
                      "hover:shadow-xl hover:border-white/40 dark:hover:border-white/20",
                      isVisible ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-8"
                    )}
                    style={{
                      transitionDelay: `${index * 100}ms`,
                      zIndex: 2, // ensure hovered card is above
                    }}
                  >
                    {/* Animated background effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/10 transform -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out pointer-events-none"></div>
                    
                    <div className="flex items-start gap-4 relative z-10">
                      <Avatar className="h-12 w-12 ring-2 ring-white/20 dark:ring-white/10 transform transition-all duration-300 group-hover:scale-110 group-hover:ring-4 group-hover:ring-white/30">
                        <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                        <AvatarFallback className="text-sm font-bold bg-white/20 dark:bg-black/20 backdrop-blur-sm text-slate-900 dark:text-white transform transition-all duration-300 group-hover:rotate-12">
                          {activity.user.initials}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 space-y-2">
                        <p className="text-sm leading-relaxed transform transition-all duration-300 group-hover:translate-x-1">
                          <span className="font-black text-slate-900 dark:text-white">{activity.user.name}</span>{" "}
                          <span className="text-slate-700 dark:text-slate-300 font-medium">{activity.action}</span>{" "}
                          <span className="font-bold text-slate-900 dark:text-white">{activity.target}</span>
                        </p>
                        <div className="flex items-center gap-2 transform transition-all duration-300 group-hover:translate-x-1">
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3 text-slate-500 dark:text-slate-400 transform transition-transform duration-300 group-hover:animate-spin" />
                            <span className="text-xs text-slate-600 dark:text-slate-400 font-medium">
                              {activity.timestamp}
                            </span>
                          </div>
                          <div className={cn(
                            "text-xs px-2 py-1 rounded-full font-bold backdrop-blur-sm border border-white/20 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-1 relative",
                            style.text,
                            "bg-white/20 dark:bg-black/20"
                          )}>
                            {activity.type}
                            {/* Activity type indicator */}
                            <div className="absolute -top-1 -right-1 w-2 h-2 bg-current rounded-full opacity-50 animate-ping"></div>
                            <div className="absolute -top-1 -right-1 w-2 h-2 bg-current rounded-full opacity-80"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Ripple effect on click */}
                    <div className="absolute inset-0 rounded-2xl opacity-0 group-active:opacity-20 bg-white dark:bg-black transition-opacity duration-150 pointer-events-none"></div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
}
