
-- Enable RLS on enrollments table (if not already enabled)
ALTER TABLE public.enrollments ENABLE ROW LEVEL SECURITY;

-- Allow anyone to view enrollments (for displaying student courses and course students)
CREATE POLICY "Allow public read access to enrollments" 
  ON public.enrollments 
  FOR SELECT 
  USING (true);

-- Allow anyone to insert new enrollments (for enrolling students in courses)
CREATE POLICY "Allow public insert access to enrollments" 
  ON public.enrollments 
  FOR INSERT 
  WITH CHECK (true);

-- Allow anyone to delete enrollments (for removing students from courses)
CREATE POLICY "Allow public delete access to enrollments" 
  ON public.enrollments 
  FOR DELETE 
  USING (true);

-- Allow anyone to update enrollments (if needed for future features)
CREATE POLICY "Allow public update access to enrollments" 
  ON public.enrollments 
  FOR UPDATE 
  USING (true);
