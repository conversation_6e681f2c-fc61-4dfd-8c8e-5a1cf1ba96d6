
-- Sample Departments
INSERT INTO departments (name) VALUES
('Computer Science'),
('Mathematics'),
('Physics'),
('Business Administration'),
('English Literature');

-- Sample Employees (teachers, finance staff, counselors, admins)
INSERT INTO employees (name, email, role, department_id, salary) VALUES
-- Teachers
('<PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Computer Science'), 75000),
('<PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Mathematics'), 72000),
('<PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Physics'), 74000),
('<PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Business Administration'), 71000),
('<PERSON>', 'micha<PERSON>.<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'English Literature'), 70000),
('<PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Computer Science'), 76000),
('<PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Mathematics'), 73000),
('<PERSON> <PERSON>', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Physics'), 74500),
('William Harris', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'Business Administration'), 72500),
('Elizabeth Clark', '<EMAIL>', 'teacher', (SELECT id FROM departments WHERE name = 'English Literature'), 71000),

-- Finance staff
('Thomas Anderson', '<EMAIL>', 'finance', NULL, 65000),
('Patricia White', '<EMAIL>', 'finance', NULL, 64000),
('Richard King', '<EMAIL>', 'finance', NULL, 66000),

-- Counselors
('Susan Lewis', '<EMAIL>', 'counselor', NULL, 60000),
('Daniel Taylor', '<EMAIL>', 'counselor', NULL, 61000),
('Karen Moore', '<EMAIL>', 'counselor', NULL, 60500),

-- Admins
('Joseph Allen', '<EMAIL>', 'admin', NULL, 85000),
('Nancy Young', '<EMAIL>', 'admin', NULL, 84000),
('Charles Hall', '<EMAIL>', 'admin', NULL, 86000),
('Margaret Adams', '<EMAIL>', 'admin', NULL, 85500);

-- Update department managers
UPDATE departments
SET manager_employee_id = e.id
FROM employees e
WHERE departments.name = 'Computer Science' AND e.name = 'Joseph Allen' AND e.role = 'admin';

UPDATE departments
SET manager_employee_id = e.id
FROM employees e
WHERE departments.name = 'Mathematics' AND e.name = 'Nancy Young' AND e.role = 'admin';

UPDATE departments
SET manager_employee_id = e.id
FROM employees e
WHERE departments.name = 'Physics' AND e.name = 'Charles Hall' AND e.role = 'admin';

UPDATE departments
SET manager_employee_id = e.id
FROM employees e
WHERE departments.name = 'Business Administration' AND e.name = 'Margaret Adams' AND e.role = 'admin';

-- Sample Courses
INSERT INTO courses (title, description, duration_months, start_date, end_date, department_id, active) VALUES
('Introduction to Programming', 'Learn the basics of programming with Python', 3, '2024-05-01', '2024-08-01', (SELECT id FROM departments WHERE name = 'Computer Science'), true),
('Data Structures and Algorithms', 'Advanced programming concepts', 4, '2024-06-01', '2024-10-01', (SELECT id FROM departments WHERE name = 'Computer Science'), true),
('Web Development', 'Learn HTML, CSS, and JavaScript', 3, '2024-05-15', '2024-08-15', (SELECT id FROM departments WHERE name = 'Computer Science'), true),
('Mobile App Development', 'Create applications for iOS and Android', 4, '2024-07-01', '2024-11-01', (SELECT id FROM departments WHERE name = 'Computer Science'), true),
('Calculus I', 'Introduction to differential calculus', 4, '2024-05-01', '2024-09-01', (SELECT id FROM departments WHERE name = 'Mathematics'), true),
('Calculus II', 'Introduction to integral calculus', 4, '2024-09-15', '2025-01-15', (SELECT id FROM departments WHERE name = 'Mathematics'), true),
('Linear Algebra', 'Study of vectors and matrices', 3, '2024-06-01', '2024-09-01', (SELECT id FROM departments WHERE name = 'Mathematics'), true),
('Statistics and Probability', 'Fundamental concepts in statistics', 3, '2024-07-15', '2024-10-15', (SELECT id FROM departments WHERE name = 'Mathematics'), true),
('Classical Mechanics', 'Introduction to Newtonian physics', 4, '2024-05-01', '2024-09-01', (SELECT id FROM departments WHERE name = 'Physics'), true),
('Electromagnetism', 'Study of electricity and magnetism', 4, '2024-09-15', '2025-01-15', (SELECT id FROM departments WHERE name = 'Physics'), true),
('Quantum Physics', 'Introduction to quantum mechanics', 5, '2024-06-01', '2024-11-01', (SELECT id FROM departments WHERE name = 'Physics'), true),
('Thermodynamics', 'Study of heat and energy', 3, '2024-07-15', '2024-10-15', (SELECT id FROM departments WHERE name = 'Physics'), true),
('Principles of Marketing', 'Introduction to marketing concepts', 3, '2024-05-01', '2024-08-01', (SELECT id FROM departments WHERE name = 'Business Administration'), true),
('Financial Accounting', 'Basics of accounting principles', 4, '2024-06-15', '2024-10-15', (SELECT id FROM departments WHERE name = 'Business Administration'), true),
('Business Ethics', 'Ethical considerations in business', 2, '2024-07-01', '2024-09-01', (SELECT id FROM departments WHERE name = 'Business Administration'), true),
('Organizational Behavior', 'Study of human behavior in organizations', 3, '2024-08-15', '2024-11-15', (SELECT id FROM departments WHERE name = 'Business Administration'), true),
('English Composition', 'Improve writing skills', 3, '2024-05-01', '2024-08-01', (SELECT id FROM departments WHERE name = 'English Literature'), true),
('American Literature', 'Study of American literary works', 4, '2024-06-15', '2024-10-15', (SELECT id FROM departments WHERE name = 'English Literature'), true),
('Shakespeare Studies', 'Analysis of Shakespeare''s plays and sonnets', 3, '2024-07-01', '2024-10-01', (SELECT id FROM departments WHERE name = 'English Literature'), true),
('Creative Writing', 'Develop creative writing skills', 2, '2024-08-15', '2024-10-15', (SELECT id FROM departments WHERE name = 'English Literature'), true);

-- Sample Students
INSERT INTO students (name, email, dob, address, contact) VALUES
('Alex Thompson', '<EMAIL>', '2000-05-12', '123 Main St, Anytown, CA', '************'),
('Emma Rodriguez', '<EMAIL>', '2001-02-23', '456 Oak Ave, Somewhere, NY', '************'),
('Ryan Jackson', '<EMAIL>', '1999-11-08', '789 Pine Rd, Nowhere, TX', '************'),
('Olivia Kim', '<EMAIL>', '2002-07-15', '101 Maple Dr, Anywhere, FL', '************'),
('Ethan Patel', '<EMAIL>', '2000-09-30', '202 Cedar Ln, Everywhere, WA', '************'),
('Sophia Nguyen', '<EMAIL>', '2001-04-18', '303 Birch Blvd, Someplace, IL', '************'),
('Noah Williams', '<EMAIL>', '1999-08-25', '404 Spruce St, Noplace, AZ', '************'),
('Ava Martinez', '<EMAIL>', '2002-01-05', '505 Willow Way, Elsewhere, OR', '************'),
('Liam Johnson', '<EMAIL>', '2000-12-14', '606 Poplar Pl, Nowhere, MI', '************'),
('Isabella Smith', '<EMAIL>', '2001-06-27', '707 Elm St, Somewhere, CO', '************'),
('Jacob Brown', '<EMAIL>', '1999-03-09', '808 Aspen Ave, Anywhere, GA', '************'),
('Mia Davis', '<EMAIL>', '2002-10-22', '909 Fir Rd, Everywhere, NC', '************'),
('Lucas Garcia', '<EMAIL>', '2000-08-03', '110 Pine Dr, Someplace, PA', '************'),
('Charlotte Wilson', '<EMAIL>', '2001-01-16', '211 Cedar Ct, Noplace, OH', '************'),
('Mason Lee', '<EMAIL>', '1999-12-29', '312 Birch St, Elsewhere, VA', '************'),
('Harper Anderson', '<EMAIL>', '2002-05-07', '413 Oak Ln, Nowhere, MN', '************'),
('Elijah Thomas', '<EMAIL>', '2000-03-20', '514 Maple Blvd, Somewhere, NJ', '************'),
('Amelia Taylor', '<EMAIL>', '2001-09-11', '615 Spruce Way, Anywhere, SC', '************'),
('Benjamin Hernandez', '<EMAIL>', '1999-07-26', '716 Willow Dr, Everywhere, TN', '************'),
('Abigail Moore', '<EMAIL>', '2002-02-08', '817 Poplar St, Someplace, KY', '************');

-- Assign teachers to courses
INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'John Smith' AND c.title = 'Introduction to Programming';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'Jennifer Lee' AND c.title = 'Data Structures and Algorithms';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'John Smith' AND c.title = 'Web Development';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'Jennifer Lee' AND c.title = 'Mobile App Development';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'Mary Johnson' AND c.title = 'Calculus I';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'David Garcia' AND c.title = 'Calculus II';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'Mary Johnson' AND c.title = 'Linear Algebra';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'David Garcia' AND c.title = 'Statistics and Probability';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'Robert Davis' AND c.title = 'Classical Mechanics';

INSERT INTO teacher_course_map (employee_id, course_id) 
SELECT e.id, c.id 
FROM employees e, courses c 
WHERE e.name = 'Jessica Martinez' AND c.title = 'Electromagnetism';

-- Enroll students in courses
DO $$
DECLARE
    student_cursor CURSOR FOR SELECT id FROM students;
    course_cursor CURSOR FOR SELECT id FROM courses;
    student_id UUID;
    course_id UUID;
    student_counter INTEGER := 0;
    max_courses INTEGER := 3;
    course_counter INTEGER := 0;
BEGIN
    OPEN student_cursor;
    
    LOOP
        FETCH student_cursor INTO student_id;
        EXIT WHEN NOT FOUND;
        
        student_counter := student_counter + 1;
        course_counter := 0;
        
        OPEN course_cursor;
        
        LOOP
            FETCH course_cursor INTO course_id;
            EXIT WHEN NOT FOUND OR course_counter >= max_courses;
            
            -- Only enroll if course_counter + student_counter is divisible by 4
            -- This creates a pattern where not all students are in all courses
            IF (course_counter + student_counter) % 4 = 0 THEN
                INSERT INTO enrollments (student_id, course_id)
                VALUES (student_id, course_id);
                
                course_counter := course_counter + 1;
            END IF;
        END LOOP;
        
        CLOSE course_cursor;
    END LOOP;
    
    CLOSE student_cursor;
END $$;

-- Add sample payments with different methods and statuses
DO $$
DECLARE
    enrollment_record RECORD;
    payment_method TEXT;
    payment_status TEXT;
    payment_amount NUMERIC;
    methods TEXT[] := ARRAY['credit_card', 'bank_transfer', 'cash', 'other'];
    statuses TEXT[] := ARRAY['pending', 'partial', 'paid'];
BEGIN
    FOR enrollment_record IN SELECT e.id, e.student_id, e.course_id FROM enrollments e
    LOOP
        -- Randomly select method, status, and amount
        payment_method := methods[(FLOOR(RANDOM() * 4) + 1)::INT];
        
        -- For demonstration purposes, set statuses with different probabilities
        IF RANDOM() < 0.6 THEN
            payment_status := 'paid';
            payment_amount := (FLOOR(RANDOM() * 500) + 500)::NUMERIC;
        ELSIF RANDOM() < 0.8 THEN
            payment_status := 'partial';
            payment_amount := (FLOOR(RANDOM() * 300) + 200)::NUMERIC;
        ELSE
            payment_status := 'pending';
            payment_amount := (FLOOR(RANDOM() * 500) + 500)::NUMERIC;
        END IF;
        
        INSERT INTO payments (
            student_id,
            course_id,
            amount,
            method,
            status,
            transaction_ref,
            paid_on
        ) VALUES (
            enrollment_record.student_id,
            enrollment_record.course_id,
            payment_amount,
            payment_method,
            payment_status,
            CASE 
                WHEN payment_method = 'credit_card' THEN 'CC-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || FLOOR(RANDOM() * 10000)::TEXT
                WHEN payment_method = 'bank_transfer' THEN 'BT-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || FLOOR(RANDOM() * 10000)::TEXT
                ELSE NULL
            END,
            CASE 
                WHEN payment_status = 'paid' THEN CURRENT_TIMESTAMP - (FLOOR(RANDOM() * 30) || ' days')::INTERVAL
                WHEN payment_status = 'partial' THEN CURRENT_TIMESTAMP - (FLOOR(RANDOM() * 20) || ' days')::INTERVAL
                ELSE NULL
            END
        );
    END LOOP;
END $$;
