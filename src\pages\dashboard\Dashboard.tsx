
import { StatCard } from "@/components/dashboard/StatCard";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { CourseProgress } from "@/components/dashboard/CourseProgress";
import { Users, BookOpen, GraduationCap, DollarSign, Activity } from "lucide-react";
import { useState, useEffect } from "react";

export default function Dashboard() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen">
      <div className="space-y-8 p-6 max-w-7xl mx-auto">
        {/* Header Section */}
        <div className={`backdrop-blur-xl bg-white/20 dark:bg-black/20 rounded-2xl border border-white/20 dark:border-white/10 p-8 shadow-xl transform transition-all duration-700 hover:scale-[1.02] hover:shadow-2xl ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-black text-slate-900 dark:text-white mb-2 transform transition-all duration-500 hover:scale-105">
                Good morning 👋
              </h1>
              <p className="text-lg text-slate-600 dark:text-slate-300 font-medium">
                Here's what's happening with your learning platform today.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <div className="backdrop-blur-xl bg-white/30 dark:bg-black/30 rounded-xl border border-white/20 dark:border-white/10 px-4 py-2 shadow-lg transform transition-all duration-300 hover:scale-105 hover:bg-white/40 dark:hover:bg-black/40">
                <div className="flex items-center gap-2">
                  <Activity className="w-4 h-4 text-green-600 dark:text-green-400 animate-pulse" />
                  <span className="text-sm font-bold text-slate-700 dark:text-slate-200">All systems operational</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Stats Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Students"
            value="2,847"
            description="↗ 12% from last month"
            icon={<Users className="w-6 h-6" />}
            trend={{ value: 12, isPositive: true }}
            className={`backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 hover:bg-white/15 dark:hover:bg-white/8 transform transition-all duration-700 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
            style={{ transitionDelay: '200ms' } as React.CSSProperties}
          />
          <StatCard
            title="Active Courses"
            value="156"
            description="↗ 8% from last month"
            icon={<BookOpen className="w-6 h-6" />}
            trend={{ value: 8, isPositive: true }}
            className={`backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 hover:bg-white/15 dark:hover:bg-white/8 transform transition-all duration-700 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
            style={{ transitionDelay: '300ms' } as React.CSSProperties}
          />
          <StatCard
            title="Faculty Members"
            value="84"
            description="↗ 3% from last month"
            icon={<GraduationCap className="w-6 h-6" />}
            trend={{ value: 3, isPositive: true }}
            className={`backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 hover:bg-white/15 dark:hover:bg-white/8 transform transition-all duration-700 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
            style={{ transitionDelay: '400ms' } as React.CSSProperties}
          />
          <StatCard
            title="Revenue"
            value="$89.2k"
            description="↗ 23% from last month"
            icon={<DollarSign className="w-6 h-6" />}
            trend={{ value: 23, isPositive: true }}
            className={`backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 hover:bg-white/15 dark:hover:bg-white/8 transform transition-all duration-700 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
            style={{ transitionDelay: '500ms' } as React.CSSProperties}
          />
        </div>
        
        {/* Main Content Grid */}
        <div className="grid gap-8 lg:grid-cols-12">
          {/* Left Column */}
          <div className="lg:col-span-8 space-y-8">
            <div className={`backdrop-blur-xl bg-white/30 dark:bg-black/20 rounded-2xl border border-white/20 dark:border-white/10 shadow-xl overflow-hidden transform transition-all duration-700 hover:scale-[1.02] hover:shadow-2xl ${isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-8'}`} style={{ transitionDelay: '600ms' }}>
              <CourseProgress />
            </div>
          </div>
          
          {/* Right Column */}
          <div className="lg:col-span-4 space-y-8">
            <div className={`backdrop-blur-xl bg-white/30 dark:bg-black/20 rounded-2xl border border-white/20 dark:border-white/10 shadow-xl overflow-hidden transform transition-all duration-700 hover:scale-[1.02] hover:shadow-2xl ${isLoaded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'}`} style={{ transitionDelay: '700ms' }}>
              <QuickActions />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

