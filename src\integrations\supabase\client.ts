// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zpgyikcpoaioldomuutp.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpwZ3lpa2Nwb2Fpb2xkb211dXRwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5ODMwNzcsImV4cCI6MjA2NTU1OTA3N30.ZA5nq2bB_dn8Bzv-bz32HNfjguRrmNSVY8sURnKNegY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);