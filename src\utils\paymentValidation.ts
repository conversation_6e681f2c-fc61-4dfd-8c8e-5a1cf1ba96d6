
import { PaymentMethod, PaymentStatus } from "@/types/payment";

// Helper function to ensure payment method is valid
export const validatePaymentMethod = (method: string): PaymentMethod => {
  if (['credit_card', 'bank_transfer', 'cash', 'other'].includes(method)) {
    return method as PaymentMethod;
  }
  return 'other'; // Default to 'other' if not valid
};

// Helper function to ensure payment status is valid
export const validatePaymentStatus = (status: string): PaymentStatus => {
  if (['pending', 'partial', 'paid', 'overdue'].includes(status)) {
    return status as PaymentStatus;
  }
  return 'pending'; // Default to 'pending' if not valid
};

// Helper function to transform database rows to Payment type
export const transformPayment = (payment: any): import("@/types/payment").Payment => {
  return {
    ...payment,
    method: validatePaymentMethod(payment.method),
    status: validatePaymentStatus(payment.status)
  };
};
