
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { Employee } from "./useEmployeeDetails";

export const useEmployees = () => {
  const queryClient = useQueryClient();

  const { data: employees = [], isLoading, error } = useQuery({
    queryKey: ["employees"],
    queryFn: async () => {
      console.log("Fetching employees...");
      const { data, error } = await supabase
        .from("employees")
        .select(`
          *,
          department:departments!employees_department_id_fkey(name),
          teaches(
            course:courses(title)
          )
        `)
        .order('name', { ascending: true });

      if (error) {
        console.error("Error fetching employees:", error);
        throw error;
      }
      
      console.log("Employees data:", data);
      return data || [];
    },
  });

  const addEmployee = async (employeeData: Omit<Employee, 'id' | 'created_at'>) => {
    const { data, error } = await supabase
      .from("employees")
      .insert(employeeData)
      .select();

    if (error) throw error;
    return data[0];
  };

  const { mutateAsync: addEmployeeMutation } = useMutation({
    mutationFn: (employeeData: Omit<Employee, 'id' | 'created_at'>) =>
      addEmployee(employeeData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employees"] });
      toast({
        title: "Success",
        description: "Employee added successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to add employee",
        variant: "destructive",
      });
      console.error("Error adding employee:", error);
    },
  });

  const deleteEmployee = async (employeeId: string) => {
    const { error } = await supabase
      .from("employees")
      .delete()
      .eq("id", employeeId);

    if (error) throw error;
    return true;
  };

  const { mutateAsync: deleteEmployeeMutation } = useMutation({
    mutationFn: (employeeId: string) => deleteEmployee(employeeId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employees"] });
      toast({
        title: "Success",
        description: "Employee deleted successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete employee",
        variant: "destructive",
      });
      console.error("Error deleting employee:", error);
    },
  });

  return {
    employees,
    isLoading,
    error,
    addEmployee: addEmployeeMutation,
    deleteEmployee: deleteEmployeeMutation
  };
};
