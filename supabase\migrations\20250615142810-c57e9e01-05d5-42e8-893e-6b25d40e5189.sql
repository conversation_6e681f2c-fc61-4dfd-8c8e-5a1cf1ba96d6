
-- Enable RLS for teaches table if not enabled already
ALTER TABLE public.teaches ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to view teacher assignments
CREATE POLICY "Authenticated can view teaches"
  ON public.teaches
  FOR SELECT
  TO authenticated
  USING (true);

-- Allow authenticated users to assign a teacher to a course (insert)
CREATE POLICY "Authenticated can assign teacher (insert)"
  ON public.teaches
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Allow authenticated users to unassign a teacher from a course (delete)
CREATE POLICY "Authenticated can unassign teacher (delete)"
  ON public.teaches
  FOR DELETE
  TO authenticated
  USING (true);

-- If you want to restrict assignment management to only 'admin' users, let me know!
