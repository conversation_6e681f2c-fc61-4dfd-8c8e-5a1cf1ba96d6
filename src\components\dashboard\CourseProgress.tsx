import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { TrendingUp, Users, Clock } from "lucide-react";
import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";

type Course = {
  id: string;
  name: string;
  enrolled: number;
  capacity: number;
  progress: number;
  status: string;
  textColor: string;
};

const courses: Course[] = [
  {
    id: "1",
    name: "Web Development Fundamentals",
    enrolled: 18,
    capacity: 25,
    progress: 72,
    status: "Active",
    textColor: "text-black dark:text-white"
  },
  {
    id: "2",
    name: "Data Science Bootcamp",
    enrolled: 12,
    capacity: 20,
    progress: 60,
    status: "Active",
    textColor: "text-black dark:text-white"
  },
  {
    id: "3",
    name: "UX Design Principles",
    enrolled: 8,
    capacity: 15,
    progress: 53,
    status: "Active",
    textColor: "text-black dark:text-white"
  },
  {
    id: "4",
    name: "Mobile App Development",
    enrolled: 15,
    capacity: 20,
    progress: 75,
    status: "Active",
    textColor: "text-black dark:text-white"
  },
];

export function CourseProgress() {
  const [animatedProgress, setAnimatedProgress] = useState<Record<string, number>>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const timer = setTimeout(() => {
      const progressMap: Record<string, number> = {};
      courses.forEach(course => {
        progressMap[course.id] = course.progress;
      });
      setAnimatedProgress(progressMap);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  return (
    <Card className="border-0 shadow-none bg-transparent">
      <CardHeader className="flex flex-row items-center justify-between pb-6">
        <div className="flex items-center gap-3">
          <div className="backdrop-blur-md bg-white/20 dark:bg-white/10 p-2 rounded-lg border border-white/30 dark:border-white/20 transform transition-all duration-300 hover:scale-110 hover:rotate-6">
            <TrendingUp className="w-4 h-4 text-slate-700 dark:text-slate-200 transform transition-transform duration-300 hover:animate-pulse" />
          </div>
          <div>
            <CardTitle className="text-xl font-black text-slate-900 dark:text-white">Course Performance</CardTitle>
            <CardDescription className="text-slate-600 dark:text-slate-400 font-medium">
              Track enrollment and completion rates
            </CardDescription>
          </div>
        </div>
        <select className="text-sm font-bold backdrop-blur-xl bg-white/20 dark:bg-white/10 border border-white/20 dark:border-white/10 rounded-xl px-4 py-2 text-slate-700 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-white/30 transition-all duration-300 hover:scale-105 hover:bg-white/30 dark:hover:bg-white/15">
          <option>This Month</option>
          <option>Last Month</option>
          <option>This Quarter</option>
        </select>
      </CardHeader>
      <CardContent className="space-y-6">
        {courses.map((course, index) => (
          <div 
            key={course.id} 
            className={cn(
              "backdrop-blur-xl bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 rounded-2xl p-6 hover:scale-105 hover:-translate-y-1 transition-all duration-500 group cursor-pointer relative overflow-hidden transform-gpu",
              "hover:shadow-2xl hover:border-white/40 dark:hover:border-white/30 hover:bg-white/15 dark:hover:bg-white/8",
              isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
            )}
            style={{
              transitionDelay: `${index * 150}ms`,
            }}
          >
            {/* Glassmorphism shimmer effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
            
            <div className="flex items-start justify-between mb-4 relative z-10">
              <div className="flex-1">
                <h4 className="font-bold text-slate-900 dark:text-white text-lg mb-2 group-hover:text-slate-800 dark:group-hover:text-slate-100 transition-all duration-300 transform group-hover:translate-x-1">
                  {course.name}
                </h4>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1 transform transition-all duration-300 group-hover:scale-105">
                    <Users className="w-4 h-4 text-slate-600 dark:text-slate-400 transform transition-transform duration-300 group-hover:animate-bounce" />
                    <span className="font-bold text-slate-700 dark:text-slate-300 tabular-nums">
                      {course.enrolled}/{course.capacity} enrolled
                    </span>
                  </div>
                  <div className={cn(
                    "px-3 py-1 rounded-full text-xs font-bold backdrop-blur-md bg-white/20 dark:bg-white/10 border border-white/30 dark:border-white/20 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-1",
                    "text-slate-700 dark:text-slate-200"
                  )}>
                    <span className="relative">
                      {course.status}
                      <div className="absolute -top-1 -right-1 w-2 h-2 bg-white/60 dark:bg-white/40 rounded-full animate-ping opacity-30"></div>
                      <div className="absolute -top-1 -right-1 w-2 h-2 bg-white/80 dark:bg-white/60 rounded-full"></div>
                    </span>
                  </div>
                </div>
              </div>
              <div className="text-right transform transition-all duration-300 group-hover:scale-110">
                <div className={cn("text-2xl font-black tabular-nums transition-all duration-300", course.textColor)}>
                  {animatedProgress[course.id] || 0}%
                </div>
                <div className="text-xs text-slate-600 dark:text-slate-400 font-medium flex items-center gap-1 justify-end">
                  <Clock className="w-3 h-3 transform transition-transform duration-300 group-hover:animate-spin" />
                  completion
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="h-3 backdrop-blur-sm bg-white/20 dark:bg-white/10 border border-white/20 dark:border-white/10 rounded-full overflow-hidden transition-all duration-300 group-hover:h-4">
                <div 
                  className="h-full bg-gradient-to-r from-blue-400/60 via-purple-400/60 to-pink-400/60 rounded-full transition-all duration-1000 ease-out"
                  style={{ 
                    width: `${animatedProgress[course.id] || 0}%`,
                    transition: 'width 1s cubic-bezier(0.4, 0, 0.2, 1)'
                  }}
                ></div>
              </div>
            </div>
            
            {/* Glass floating completion indicator */}
            {(animatedProgress[course.id] || 0) > 70 && (
              <div className="absolute top-4 right-4 w-2 h-2 bg-white/60 dark:bg-white/40 rounded-full opacity-0 group-hover:opacity-100 transform group-hover:animate-pulse transition-all duration-500"></div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}