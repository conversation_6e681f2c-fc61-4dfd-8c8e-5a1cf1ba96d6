
import { useParams, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useEmployeeDetails } from "@/hooks/useEmployeeDetails";
import { AssignCourseDialog } from "@/components/employees/AssignCourseDialog";
import { ArrowLeft } from "lucide-react";

export default function EmployeeAssignedCourses() {
  const { employeeId } = useParams();
  const navigate = useNavigate();
  const { employeeDetails, loading } = useEmployeeDetails(employeeId);

  const handleCourseAssigned = () => {
    // Refetch employee details to show the newly assigned course
    window.location.reload();
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Loading courses...</div>
        </div>
      </div>
    );
  }

  if (!employeeDetails) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Employee not found</div>
        </div>
      </div>
    );
  }

  const assignedCourseIds = employeeDetails.teaches?.map((teaching: any) => teaching.course.id) || [];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={() => navigate(`/employees/${employeeId}`)}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Employee Details
        </Button>
        
        <AssignCourseDialog
          employeeId={employeeId!}
          employeeName={employeeDetails.name}
          assignedCourseIds={assignedCourseIds}
          onCourseAssigned={handleCourseAssigned}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Courses Managed by {employeeDetails.name}</span>
            <Badge variant="outline">{employeeDetails.role}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {employeeDetails.teaches && employeeDetails.teaches.length > 0 ? (
              employeeDetails.teaches.map((teaching: any) => (
                <Card 
                  key={teaching.course.id} 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => navigate(`/courses/${teaching.course.id}`)}
                >
                  <CardContent className="p-4">
                    <h4 className="font-medium">{teaching.course.title}</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      {teaching.course.description?.substring(0, 100)}
                      {teaching.course.description?.length > 100 ? "..." : ""}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline" className="bg-blue-accent/10 text-blue-accent">
                        {teaching.course.active ? "Active" : "Inactive"}
                      </Badge>
                      {teaching.course.department?.name && (
                        <Badge variant="outline">
                          {teaching.course.department.name}
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <p className="text-muted-foreground col-span-full">
                No courses assigned to this employee yet.
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
