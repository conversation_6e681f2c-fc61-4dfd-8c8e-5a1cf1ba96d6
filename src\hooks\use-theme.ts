import { useState, useEffect } from "react";
import { useToast } from "./use-toast";

type Theme = "light" | "dark";

export function useTheme() {
  const [theme, setThemeState] = useState<Theme>("light");
  const { toast } = useToast();

  // Load theme from localStorage on initial render
  useEffect(() => {
    const savedTheme = localStorage.getItem("theme") as Theme | null;
    
    // Check for saved theme or use system preference
    if (savedTheme) {
      setThemeState(savedTheme);
      document.documentElement.classList.toggle("dark", savedTheme === "dark");
    } else if (window.matchMedia("(prefers-color-scheme: dark)").matches) {
      setThemeState("dark");
      document.documentElement.classList.add("dark");
    }
  }, []);

  // Set theme with optional notification
  const setTheme = (newTheme: Theme, notify = false) => {
    setThemeState(newTheme);
    document.documentElement.classList.toggle("dark", newTheme === "dark");
    localStorage.setItem("theme", newTheme);
    
    if (notify) {
      toast({
        title: `${newTheme.charAt(0).toUpperCase() + newTheme.slice(1)} mode activated`,
        description: `The application theme has been changed to ${newTheme} mode.`,
      });
    }
  };

  // Toggle between light and dark themes
  const toggleTheme = (notify = true) => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme, notify);
  };

  // Use system preference
  const useSystemTheme = () => {
    const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
    setThemeState(prefersDark ? "dark" : "light");
    document.documentElement.classList.toggle("dark", prefersDark);
    localStorage.removeItem("theme");
    
    toast({
      title: "System preference applied",
      description: "Theme will now follow your system settings.",
    });
  };

  return {
    theme,
    setTheme,
    toggleTheme,
    useSystemTheme,
  };
}
