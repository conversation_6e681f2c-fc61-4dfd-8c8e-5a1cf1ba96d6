
import { Course } from "@/hooks/useCourses";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { useState } from "react";

interface StudentCoursesProps {
  courses: Course[];
  availableCourses: Course[];
  onEnrollCourse: (courseId: string) => Promise<void>;
  enrollLoading?: boolean;
}

export const StudentCourses = ({
  courses,
  availableCourses,
  onEnrollCourse,
  enrollLoading = false
}: StudentCoursesProps) => {
  const [selectedCourse, setSelectedCourse] = useState<string>("");
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleEnrollCourse = async () => {
    if (selectedCourse) {
      await onEnrollCourse(selectedCourse);
      setSelectedCourse("");
      setDialogOpen(false);
    }
  };

  return (
    <div className="relative">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-semibold">Courses Enrolled</h2>
        <Dialog open={dialogOpen} onOpenChange={(open) => { if (!enrollLoading) setDialogOpen(open); }}>
          <DialogTrigger asChild>
            <Button disabled={enrollLoading}>
              <Plus className="mr-2 h-4 w-4" />
              Enroll in Course
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Enroll in Course</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a course" />
                </SelectTrigger>
                <SelectContent>
                  {availableCourses.length === 0 ? (
                    <div className="px-4 py-2 text-muted-foreground">No courses available</div>
                  ) : (
                    availableCourses.map(course => (
                      <SelectItem key={course.id} value={course.id}>
                        {course.title}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              <Button 
                onClick={handleEnrollCourse} 
                disabled={!selectedCourse || enrollLoading}
                aria-busy={enrollLoading}
              >
                {enrollLoading ? (
                  <span className="flex items-center gap-2">
                    <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                      />
                    </svg>
                    Enrolling...
                  </span>
                ) : "Enroll"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
      {/* Loading overlay */}
      {enrollLoading && (
        <div className="absolute inset-0 bg-background/75 flex items-center justify-center z-10 rounded-md animate-fade-in">
          <svg className="animate-spin h-8 w-8 text-primary" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
            />
          </svg>
        </div>
      )}
      <div className={enrollLoading ? "opacity-60 pointer-events-none" : "animate-fade-in"}>
        {courses.length > 0 ? (
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Course Title</TableHead>
                  <TableHead>Duration (Months)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {courses.map(course => (
                  <TableRow key={course.id}>
                    <TableCell className="font-medium">{course.title}</TableCell>
                    <TableCell>{course.duration_months}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        ) : (
          <Card className="p-6">
            <p className="text-muted-foreground text-center">No courses enrolled</p>
          </Card>
        )}
      </div>
    </div>
  );
};
