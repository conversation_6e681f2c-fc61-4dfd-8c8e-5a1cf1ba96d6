
import { useEffect, useRef } from 'react';
import { 
  Chart, 
  registerables, 
  ChartType, 
  ChartData, 
  ChartOptions 
} from 'chart.js';

// Register all Chart.js components
Chart.register(...registerables);

interface QueryResultsChartProps {
  type: string;
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
    }[];
  };
}

export function QueryResultsChart({ type, data }: QueryResultsChartProps) {
  const chartRef = useRef<HTMLCanvasElement | null>(null);
  const chartInstance = useRef<Chart | null>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    // Destroy previous chart if it exists
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // Create color palette
    const colors = [
      'rgba(75, 192, 192, 0.7)',
      'rgba(54, 162, 235, 0.7)',
      'rgba(255, 99, 132, 0.7)',
      'rgba(255, 206, 86, 0.7)',
      'rgba(153, 102, 255, 0.7)',
      'rgba(255, 159, 64, 0.7)',
      'rgba(199, 199, 199, 0.7)',
      'rgba(83, 102, 255, 0.7)',
    ];

    // Apply colors to dataset
    const dataWithColors = {
      ...data,
      datasets: data.datasets.map((dataset, index) => ({
        ...dataset,
        backgroundColor: type === 'pie' ? colors : colors[index % colors.length],
        borderColor: type === 'line' ? colors[index % colors.length] : undefined,
        borderWidth: 1,
      })),
    };

    // Create chart with proper TypeScript typing
    const chartOptions: ChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          enabled: true,
        },
      },
    };

    // Create chart with proper typing
    chartInstance.current = new Chart(chartRef.current, {
      type: type as ChartType,
      data: dataWithColors as ChartData,
      options: chartOptions,
    });

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [type, data]);

  return <canvas ref={chartRef} />;
}
