
import { useState, useEffect } from "react";
import { usePayments } from "@/hooks/usePayments";
import { PaymentsSummaryCards } from "@/components/payments/PaymentsSummaryCards";
import { PaymentsSearchAndFilters } from "@/components/payments/PaymentsSearchAndFilters";
import { PaymentsTable } from "@/components/payments/PaymentsTable";

export default function Payments() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [methodFilter, setMethodFilter] = useState<string[]>([]);
  const [studentFilter, setStudentFilter] = useState("");
  const [courseFilter, setCourseFilter] = useState("");

  const { payments, loading, fetchPayments } = usePayments();

  console.log("Payments component data:", payments);
  console.log("Loading state:", loading);

  useEffect(() => {
    fetchPayments();
  }, []);

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = searchQuery === "" || 
      payment.student?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.course?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.transaction_ref?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter.length === 0 || statusFilter.includes(payment.status);
    const matchesMethod = methodFilter.length === 0 || methodFilter.includes(payment.method);
    const matchesStudent = studentFilter === "" || payment.student?.name === studentFilter;
    const matchesCourse = courseFilter === "" || payment.course?.title === courseFilter;

    return matchesSearch && matchesStatus && matchesMethod && matchesStudent && matchesCourse;
  });

  const totalAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const paidAmount = payments
    .filter(payment => payment.status === 'paid')
    .reduce((sum, payment) => sum + payment.amount, 0);
  const pendingAmount = payments
    .filter(payment => payment.status === 'pending')
    .reduce((sum, payment) => sum + payment.amount, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Payments</h1>
          <p className="text-muted-foreground">
            Track and manage all student payments and financial transactions
          </p>
        </div>
      </div>

      <PaymentsSummaryCards 
        totalAmount={totalAmount}
        paidAmount={paidAmount}
        pendingAmount={pendingAmount}
        totalPayments={payments.length}
      />

      {!loading && payments.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p>No payments found in the database.</p>
          <p className="text-sm">Payments will appear here when students make transactions.</p>
        </div>
      )}

      <PaymentsSearchAndFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        methodFilter={methodFilter}
        onMethodFilterChange={setMethodFilter}
        studentFilter={studentFilter}
        onStudentFilterChange={setStudentFilter}
        courseFilter={courseFilter}
        onCourseFilterChange={setCourseFilter}
        payments={payments}
      />

      <PaymentsTable 
        payments={filteredPayments}
        isLoading={loading}
      />
    </div>
  );
}
