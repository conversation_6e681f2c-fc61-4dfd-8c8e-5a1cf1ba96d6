
import { useState } from 'react';
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

export type Enrollment = {
  id: string;
  student_id: string;
  course_id: string;
  enrolled_on: string;
  student?: {
    name: string;
    email: string;
  };
};

export const useEnrollments = (courseId?: string) => {
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchEnrollments = async (cId: string = courseId || '') => {
    if (!cId) return;
    
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('enrollments')
        .select(`
          *,
          student:students(name, email)
        `)
        .eq('course_id', cId)
        .order('enrolled_on', { ascending: false });

      if (error) throw error;

      setEnrollments(data);
      return data;
    } catch (err) {
      console.error('Error fetching enrollments:', err);
      toast({
        title: "Error fetching enrollments",
        description: (err as Error).message || "Something went wrong",
        variant: "destructive",
      });
      return [];
    } finally {
      setLoading(false);
    }
  };

  const enrollStudent = async (studentId: string, cId: string = courseId || '') => {
    if (!cId) return null;
    
    try {
      // Check if student is already enrolled
      const { data: existingEnrollment, error: checkError } = await supabase
        .from('enrollments')
        .select('id')
        .eq('student_id', studentId)
        .eq('course_id', cId)
        .maybeSingle();
      
      if (checkError) throw checkError;
      
      if (existingEnrollment) {
        toast({
          title: "Student already enrolled",
          description: "This student is already enrolled in this course",
        });
        return existingEnrollment;
      }
      
      const { data, error } = await supabase
        .from('enrollments')
        .insert({
          student_id: studentId,
          course_id: cId
        })
        .select(`
          *,
          student:students(name, email)
        `)
        .single();

      if (error) throw error;

      setEnrollments(prev => [data, ...prev]);
      
      toast({
        title: "Student enrolled",
        description: "The student has been successfully enrolled",
      });
      
      return data;
    } catch (err) {
      console.error('Error enrolling student:', err);
      toast({
        title: "Error enrolling student",
        description: (err as Error).message || "Something went wrong",
        variant: "destructive",
      });
      return null;
    }
  };

  const removeEnrollment = async (enrollmentId: string) => {
    try {
      const { error } = await supabase
        .from('enrollments')
        .delete()
        .eq('id', enrollmentId);

      if (error) throw error;

      setEnrollments(prev => prev.filter(e => e.id !== enrollmentId));
      
      toast({
        title: "Enrollment removed",
        description: "The student has been removed from this course",
      });
      
      return true;
    } catch (err) {
      console.error('Error removing enrollment:', err);
      toast({
        title: "Error removing enrollment",
        description: (err as Error).message || "Something went wrong",
        variant: "destructive",
      });
      return false;
    }
  };

  return {
    enrollments,
    loading,
    fetchEnrollments,
    enrollStudent,
    removeEnrollment
  };
};
