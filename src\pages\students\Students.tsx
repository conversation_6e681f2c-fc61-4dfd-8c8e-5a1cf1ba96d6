
import { useState, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { useStudents } from "@/hooks/useStudents";
import { StudentHeader } from "@/components/students/StudentHeader";
import { SearchBar } from "@/components/students/SearchBar";
import { StudentTable } from "@/components/students/StudentTable";
import { useQueryClient } from "@tanstack/react-query";

export default function Students() {
  const [searchQuery, setSearchQuery] = useState("");
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string[]>([]);
  const [coursesFilter, setCoursesFilter] = useState<string[]>([]);
  const { toast } = useToast();
  const { data: students = [], isLoading, error } = useStudents();
  const queryClient = useQueryClient();
  
  console.log("Students component data:", students);
  console.log("Loading state:", isLoading);
  
  if (error) {
    console.error("Error loading students:", error);
    toast({
      variant: "destructive",
      title: "Error loading students",
      description: "Please try again later"
    });
  }

  // Get unique courses for filter options
  const availableCourses = useMemo(() => {
    const courses = new Set<string>();
    students.forEach(student => {
      student.coursesEnrolled.forEach(course => courses.add(course));
    });
    return Array.from(courses).sort();
  }, [students]);

  const filteredStudents = useMemo(() => {
    return students.filter(student => {
      // Search filter
      const matchesSearch = searchQuery === "" || 
        student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.email.toLowerCase().includes(searchQuery.toLowerCase());

      // Payment status filter
      const matchesPaymentStatus = paymentStatusFilter.length === 0 ||
        paymentStatusFilter.includes(student.paymentStatus);

      // Courses filter
      const matchesCourses = coursesFilter.length === 0 ||
        coursesFilter.some(course => student.coursesEnrolled.includes(course));

      return matchesSearch && matchesPaymentStatus && matchesCourses;
    });
  }, [students, searchQuery, paymentStatusFilter, coursesFilter]);

  const handleStudentDeleted = () => {
    // Refetch the students data to update the list
    queryClient.invalidateQueries({ queryKey: ["students"] });
  };
  
  return (
    <div className="space-y-6">
      <StudentHeader />
      {!isLoading && students.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p>No students found in the database.</p>
          <p className="text-sm">Add some students to see them here.</p>
        </div>
      )}
      <SearchBar 
        searchQuery={searchQuery} 
        onSearchChange={setSearchQuery}
        paymentStatusFilter={paymentStatusFilter}
        onPaymentStatusFilterChange={setPaymentStatusFilter}
        coursesFilter={coursesFilter}
        onCoursesFilterChange={setCoursesFilter}
        availableCourses={availableCourses}
      />
      <StudentTable 
        students={filteredStudents} 
        isLoading={isLoading}
        onStudentDeleted={handleStudentDeleted}
      />
    </div>
  );
}
