
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowUpRight } from "lucide-react";

interface PaymentsSummaryCardsProps {
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  totalPayments: number;
}

export function PaymentsSummaryCards({ 
  totalAmount, 
  paidAmount, 
  pendingAmount, 
  totalPayments 
}: PaymentsSummaryCardsProps) {
  const cardBase = "group transition-all duration-300 transform hover:scale-[1.03] hover:shadow-2xl overflow-hidden relative cursor-pointer";
  const particles =
    <>
      <div className="absolute top-2 right-3 w-1 h-1 bg-blue-accent opacity-0 group-hover:opacity-100 rounded-full transition-all duration-500 group-hover:animate-pulse"></div>
      <div className="absolute top-4 right-6 w-1 h-1 bg-warning opacity-0 group-hover:opacity-100 rounded-full transition-all duration-700 delay-75 group-hover:animate-pulse"></div>
      <div className="absolute bottom-4 left-4 w-1 h-1 bg-success opacity-0 group-hover:opacity-100 rounded-full transition-all duration-700 delay-150 group-hover:animate-ping"></div>
    </>;
  const accentBar =
    <div className="absolute bottom-0 left-0 w-full h-1 opacity-60 bg-gradient-to-r from-blue-accent via-success to-warning transform -translate-x-full group-hover:translate-x-0 transition-transform duration-700" />;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {/* Total Revenue Card */}
      <Card className={cardBase}>
        {/* Animated particles */}
        {particles}
        {/* Animated accent bar */}
        {accentBar}
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Total Revenue
          </CardTitle>
          <ArrowUpRight className="h-4 w-4 text-success" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${paidAmount.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            From completed payments
          </p>
        </CardContent>
      </Card>

      {/* Pending Amount Card */}
      <Card className={cardBase}>
        {particles}
        {accentBar}
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Pending Amount
          </CardTitle>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            className="h-4 w-4 text-warning"
          >
            <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
          </svg>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">${pendingAmount.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            From pending payments
          </p>
        </CardContent>
      </Card>

      {/* Total Payments Card */}
      <Card className={cardBase}>
        {particles}
        {accentBar}
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Total Payments
          </CardTitle>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            className="h-4 w-4 text-muted-foreground"
          >
            <rect width="20" height="14" x="2" y="5" rx="2" />
            <path d="M2 10h20" />
          </svg>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalPayments}</div>
          <p className="text-xs text-muted-foreground">
            Total number of payments
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
