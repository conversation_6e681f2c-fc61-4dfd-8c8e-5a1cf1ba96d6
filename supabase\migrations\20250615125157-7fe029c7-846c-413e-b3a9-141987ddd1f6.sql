
-- Sample data already inserts students/enrollments/courses as before.

-- Remove previous inserted payments if needed (optional, uncomment if re-running)
-- DELETE FROM payments;

-- Add sample payments with only 'pending', 'partial', or 'paid' status for each payment
DO $$
DECLARE
    enrollment_record RECORD;
    payment_method TEXT;
    payment_status TEXT;
    payment_amount NUMERIC;
    methods TEXT[] := ARRAY['credit_card', 'bank_transfer', 'cash', 'other'];
    statuses TEXT[] := ARRAY['pending', 'partial', 'paid'];
BEGIN
    FOR enrollment_record IN SELECT e.id, e.student_id, e.course_id FROM enrollments e
    LOOP
        -- Randomly select method, status, and amount
        payment_method := methods[(FLOOR(RANDOM() * 4) + 1)::INT];
        
        -- Now only valid statuses
        IF RANDOM() < 0.6 THEN
            payment_status := 'paid';
            payment_amount := (FLOOR(RANDOM() * 500) + 500)::NUMERIC;
        ELSIF RANDOM() < 0.8 THEN
            payment_status := 'partial';
            payment_amount := (FLOOR(RANDOM() * 300) + 200)::NUMERIC;
        ELSE
            payment_status := 'pending';
            payment_amount := (FLOOR(RANDOM() * 500) + 500)::NUMERIC;
        END IF;
        
        INSERT INTO payments (
            student_id,
            course_id,
            amount,
            method,
            status,
            transaction_ref,
            paid_on
        ) VALUES (
            enrollment_record.student_id,
            enrollment_record.course_id,
            payment_amount,
            payment_method,
            payment_status,
            CASE 
                WHEN payment_method = 'credit_card' THEN 'CC-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || FLOOR(RANDOM() * 10000)::TEXT
                WHEN payment_method = 'bank_transfer' THEN 'BT-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || FLOOR(RANDOM() * 10000)::TEXT
                ELSE NULL
            END,
            CASE 
                WHEN payment_status = 'paid' THEN CURRENT_TIMESTAMP - (FLOOR(RANDOM() * 30) || ' days')::INTERVAL
                WHEN payment_status = 'partial' THEN CURRENT_TIMESTAMP - (FLOOR(RANDOM() * 20) || ' days')::INTERVAL
                ELSE NULL
            END
        );
    END LOOP;
END $$;
