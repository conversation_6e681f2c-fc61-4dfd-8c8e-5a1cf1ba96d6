
-- Add missing foreign key constraint for departments manager (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_manager_employee' 
        AND table_name = 'departments'
    ) THEN
        ALTER TABLE departments
        ADD CONSTRAINT fk_manager_employee
        FOR<PERSON><PERSON><PERSON> KEY (manager_employee_id) REFERENCES employees(id);
    END IF;
END $$;

-- Add check constraint for employee roles (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'chk_employee_role' 
        AND table_name = 'employees'
    ) THEN
        ALTER TABLE employees
        ADD CONSTRAINT chk_employee_role
        CHECK (role IN ('teacher', 'finance', 'counselor', 'admin'));
    END IF;
END $$;

-- Make email unique in employees table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'uk_employees_email' 
        AND table_name = 'employees'
    ) THEN
        ALTER TABLE employees
        ADD CONSTRAINT uk_employees_email UNIQUE (email);
    END IF;
END $$;

-- Make email unique in students table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'uk_students_email' 
        AND table_name = 'students'
    ) THEN
        ALTER TABLE students
        ADD CONSTRAINT uk_students_email UNIQUE (email);
    END IF;
END $$;

-- Add unique constraint to enrollments (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'uk_student_course' 
        AND table_name = 'enrollments'
    ) THEN
        ALTER TABLE enrollments
        ADD CONSTRAINT uk_student_course UNIQUE (student_id, course_id);
    END IF;
END $$;

-- Add unique constraint to teaches (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'uk_employee_course' 
        AND table_name = 'teaches'
    ) THEN
        ALTER TABLE teaches
        ADD CONSTRAINT uk_employee_course UNIQUE (employee_id, course_id);
    END IF;
END $$;

-- Add check constraints for payment methods (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'chk_payment_method' 
        AND table_name = 'payments'
    ) THEN
        ALTER TABLE payments
        ADD CONSTRAINT chk_payment_method
        CHECK (method IN ('credit_card', 'bank_transfer', 'cash', 'other'));
    END IF;
END $$;

-- Update payment status check constraint
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'chk_payment_status' 
        AND table_name = 'payments'
    ) THEN
        ALTER TABLE payments DROP CONSTRAINT chk_payment_status;
    END IF;
    
    -- Add the new constraint
    ALTER TABLE payments
    ADD CONSTRAINT chk_payment_status
    CHECK (status IN ('pending', 'partial', 'paid'));
END $$;

-- Update foreign key constraints with CASCADE options
-- First, drop existing constraints and recreate them with proper CASCADE behavior
ALTER TABLE enrollments DROP CONSTRAINT IF EXISTS enrollments_student_id_fkey;
ALTER TABLE enrollments DROP CONSTRAINT IF EXISTS enrollments_course_id_fkey;
ALTER TABLE teaches DROP CONSTRAINT IF EXISTS teaches_employee_id_fkey;
ALTER TABLE teaches DROP CONSTRAINT IF EXISTS teaches_course_id_fkey;
ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_student_id_fkey;
ALTER TABLE payments DROP CONSTRAINT IF EXISTS payments_course_id_fkey;

-- Recreate with proper CASCADE behavior
ALTER TABLE enrollments
ADD CONSTRAINT enrollments_student_id_fkey
FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE;

ALTER TABLE enrollments
ADD CONSTRAINT enrollments_course_id_fkey
FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE;

ALTER TABLE teaches
ADD CONSTRAINT teaches_employee_id_fkey
FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE;

ALTER TABLE teaches
ADD CONSTRAINT teaches_course_id_fkey
FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE;

ALTER TABLE payments
ADD CONSTRAINT payments_student_id_fkey
FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE;

ALTER TABLE payments
ADD CONSTRAINT payments_course_id_fkey
FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL;
