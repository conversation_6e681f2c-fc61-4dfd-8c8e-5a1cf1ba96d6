
import { useParams, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { useStudentDetails } from "@/hooks/useStudentDetails";
import { useCourses } from "@/hooks/useCourses";
import { StudentBasicInfo } from "@/components/students/StudentBasicInfo";
import { StudentCourses } from "@/components/students/StudentCourses";
import { StudentPayments } from "@/components/students/StudentPayments";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft, Pencil } from "lucide-react";

export default function StudentDetailsPage() {
  const { studentId } = useParams<{ studentId: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [enrollmentLoading, setEnrollmentLoading] = useState(false);

  const { 
    studentDetails, 
    loading, 
    error,
    fetchStudentDetails, 
    enrollStudentInCourse,
    updatePaymentStatus 
  } = useStudentDetails();
  
  const { data: courses = [] } = useCourses();

  useEffect(() => {
    const loadData = async () => {
      if (studentId) {
        setIsLoading(true);
        await fetchStudentDetails(studentId);
        setIsLoading(false);
      }
    };
    
    loadData();
  }, [studentId, fetchStudentDetails]);

  if (isLoading || loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-lg">Loading student details...</div>
        </div>
      </div>
    );
  }

  // Show error state if there's an error or no student found
  if (error || !studentDetails) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => navigate('/students')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Students
          </Button>
        </div>
        
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-lg text-muted-foreground">Student not found</p>
            <p className="text-sm text-muted-foreground mt-2">
              The student with ID "{studentId}" does not exist or you don't have permission to view it.
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Please check the URL and ensure you're using a valid student ID.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const availableCourses = courses.filter(
    course => !studentDetails.courses.some(c => c.id === course.id)
  );

  // Modified to handle enrollment loading state and force refresh after enrollment
  const handleEnrollCourse = async (courseId: string) => {
    if (studentId) {
      setEnrollmentLoading(true);
      await enrollStudentInCourse(studentId, courseId);
      await fetchStudentDetails(studentId);
      setEnrollmentLoading(false);
    }
  };

  const handleUpdatePaymentStatus = async (paymentId: string, status: 'pending' | 'paid' | 'overdue') => {
    await updatePaymentStatus(paymentId, status);
  };

  const handleEditClick = () => {
    navigate(`/students/${studentId}/edit`);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => navigate('/students')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Students
        </Button>
        
        <Button 
          variant="outline" 
          size="sm"
          onClick={handleEditClick}
        >
          <Pencil className="mr-2 h-4 w-4" />
          Edit Student
        </Button>
      </div>
      
      <StudentBasicInfo student={studentDetails} />
      
      <div className="grid md:grid-cols-2 gap-6">
        <StudentCourses 
          courses={studentDetails.courses}
          availableCourses={availableCourses}
          onEnrollCourse={handleEnrollCourse}
          enrollLoading={enrollmentLoading}
        />
        
        <StudentPayments 
          payments={studentDetails.payments}
          onUpdatePaymentStatus={handleUpdatePaymentStatus}
        />
      </div>
    </div>
  );
}
