
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface Course {
  id: string;
  title: string;
  description: string | null;
  duration_months: number;
  start_date: string | null;
  end_date: string | null;
  department_id: string | null;
  active: boolean | null;
  created_at: string;
  department?: {
    name: string;
  } | null;
  enrollment_count?: number;
  instructor?: string;
}

export const useCourses = () => {
  return useQuery({
    queryKey: ["courses"],
    queryFn: async () => {
      // Fetch courses with department and teacher information
      const { data: coursesData, error: coursesError } = await supabase
        .from("courses")
        .select(`
          *,
          department:departments(name),
          teaches(
            employee:employees(name)
          )
        `);

      if (coursesError) throw coursesError;

      // Get enrollment counts
      const coursesWithDetails = await Promise.all(
        coursesData.map(async (course: any) => {
          const { count } = await supabase
            .from("enrollments")
            .select("*", { count: "exact", head: true })
            .eq("course_id", course.id);

          return {
            ...course,
            enrollment_count: count || 0,
            instructor: course.teaches?.[0]?.employee?.name || 'Unassigned'
          };
        })
      );

      return coursesWithDetails;
    }
  });
};
