
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface Student {
  id: string;
  name: string;
  email: string;
  created_at: string;
  contact?: string;
  address?: string; 
  dob?: string;
}

export interface StudentWithPayments extends Student {
  paymentStatus: 'paid' | 'pending' | 'overdue';
  coursesEnrolled: string[];
}

export const useStudents = () => {
  return useQuery({
    queryKey: ["students"],
    queryFn: async () => {
      console.log("==== useStudents: Starting query ====");

      // Fetch all students (use explicit fields to avoid "*" ambiguity)
      const { data: studentsData, error: studentsError } = await supabase
        .from("students")
        .select("id, name, email, created_at, contact, address, dob");
      
      // Log the raw data and errors
      if (studentsError) {
        console.error("useStudents: Error fetching students:", studentsError);
        throw studentsError;
      }
      console.log("useStudents: Students raw data:", studentsData);

      if (!studentsData || studentsData.length === 0) {
        console.warn("useStudents: No students found in raw students table.");
        return [];
      }

      // Fetch details for each student
      const studentsWithDetails = await Promise.all(
        studentsData.map(async (student: any): Promise<StudentWithPayments> => {
          // Fetch enrollments for the student, include course title
          const { data: enrollments, error: enrollmentsError } = await supabase
            .from("enrollments")
            .select("courses(title)")
            .eq("student_id", student.id);
          if (enrollmentsError) {
            console.error(`Error fetching enrollments for student ${student.id} (${student.name}):`, enrollmentsError);
          } else {
            console.log(`Enrollments for ${student.name}:`, enrollments);
          }

          // Fetch payments for this student
          const { data: payments, error: paymentsError } = await supabase
            .from("payments")
            .select("amount, status")
            .eq("student_id", student.id);
          if (paymentsError) {
            console.error(`Error fetching payments for student ${student.id} (${student.name}):`, paymentsError);
          } else {
            console.log(`Payments for ${student.name}:`, payments);
          }

          const coursesEnrolled = enrollments
            ? enrollments.map((enr: any) => enr.courses?.title).filter(Boolean)
            : [];

          let paymentStatus: 'paid' | 'pending' | 'overdue' = 'pending';

          if (payments && payments.length > 0) {
            const allPaid = payments.every((p: any) => p.status === 'paid');
            const hasPending = payments.some((p: any) => p.status === 'pending' || p.status === 'partial');
            
            if (allPaid) {
              paymentStatus = 'paid';
            } else if (hasPending) {
              paymentStatus = 'pending';
            } else {
              paymentStatus = 'overdue';
            }
          }

          console.log(`[Student: ${student.name}] => paymentStatus:`, paymentStatus, "courses:", coursesEnrolled);

          return {
            id: student.id,
            name: student.name,
            email: student.email,
            created_at: student.created_at,
            contact: student.contact,
            address: student.address,
            dob: student.dob,
            paymentStatus,
            coursesEnrolled
          };
        })
      );

      console.log("==== useStudents: studentsWithDetails result ====", studentsWithDetails);

      return studentsWithDetails;
    }
  });
};
