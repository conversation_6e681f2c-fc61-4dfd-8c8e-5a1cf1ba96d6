
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Search } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { supabase } from "@/integrations/supabase/client";

const PREBUILT_QUERIES = [
  {
    id: 'students-enrolled-data-analysis',
    name: 'Students Enrolled in Data Analysis with Python',
    description: 'List all students who are enrolled in the Data Analysis with Python course'
  },
  {
    id: 'students-max-fees',
    name: 'Students Who Paid Maximum Fees',
    description: 'Find students who have paid the highest total fees'
  },
  {
    id: 'students-not-enrolled',
    name: 'Students Not Enrolled Yet',
    description: 'List all students who have not enrolled in any course'
  },
  {
    id: 'students-name-n-fullstack',
    name: 'Students Starting with N in Full Stack',
    description: 'Students whose name starts with N and enrolled in Full Stack Development'
  },
  {
    id: 'students-pending-fees-contact',
    name: 'Contact Info for Pending Fees',
    description: 'Find contact information for students with pending fees'
  },
  {
    id: 'highest-paid-employee',
    name: 'Highest Paid Employee',
    description: 'Find the employee with the highest salary'
  },
  {
    id: 'teachers-more-than-2-courses',
    name: 'Teachers with More Than 2 Courses',
    description: 'Find teachers who are handling more than 2 courses'
  },
  {
    id: 'courses-duration-more-6-months',
    name: 'Courses Duration > 6 Months',
    description: 'Count courses with duration more than 6 months'
  },
  {
    id: 'department-managers-demo-students',
    name: 'Department Managers with Demo Students',
    description: 'Find department managers who manage demo/test students'
  },
  {
    id: 'teachers-highest-students',
    name: 'Teachers with Highest Student Count',
    description: 'Find teachers handling the highest number of students'
  },
  {
    id: 'employees-salary-10k-frontend',
    name: 'Employees Salary >10k in Frontend',
    description: 'Find employees with salary > 10k in Frontend Development'
  },
  {
    id: 'students-no-teachers',
    name: 'Students Without Teachers',
    description: 'Find students for whom teachers are not allotted yet'
  },
  {
    id: 'employees-salaries-paid',
    name: 'Employees with Salaries Paid',
    description: 'Find employees whose salaries have been paid'
  },
  {
    id: 'students-under-20-java',
    name: 'Students Under 20 in Java Course',
    description: 'Find students below 20 years old enrolled in Java course'
  }
];

export function PrebuiltQueries() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any[] | null>(null);
  const [selectedQuery, setSelectedQuery] = useState<string>("");

  const executePrebuiltQuery = async () => {
    if (!selectedQuery) {
      toast({
        title: "No query selected",
        description: "Please select a prebuilt query to execute",
      });
      return;
    }

    setIsLoading(true);
    setResults(null);
    
    try {
      const { data, error } = await supabase.functions.invoke('advanced-filter', {
        body: { prebuiltQuery: selectedQuery }
      });
      
      if (error) throw error;
      
      setResults(data.results);
      
      toast({
        title: "Query executed successfully",
        description: `Found ${data.results.length} result${data.results.length !== 1 ? 's' : ''}`,
      });
    } catch (err) {
      console.error('Error executing prebuilt query:', err);
      toast({
        variant: "destructive",
        title: "Query error",
        description: (err as Error).message || "Failed to execute query"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const selectedQueryInfo = PREBUILT_QUERIES.find(q => q.id === selectedQuery);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Prebuilt Queries</CardTitle>
          <CardDescription>
            Execute complex predefined queries to get specific insights from your data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Query</label>
            <Select value={selectedQuery} onValueChange={setSelectedQuery}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a prebuilt query..." />
              </SelectTrigger>
              <SelectContent>
                {PREBUILT_QUERIES.map((query) => (
                  <SelectItem key={query.id} value={query.id}>
                    {query.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedQueryInfo && (
            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm text-muted-foreground">
                {selectedQueryInfo.description}
              </p>
            </div>
          )}

          <Button 
            onClick={executePrebuiltQuery} 
            disabled={isLoading || !selectedQuery}
            className="w-full"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Search className="h-4 w-4 mr-2" />
            )}
            Execute Query
          </Button>
        </CardContent>
      </Card>

      {isLoading && (
        <div className="flex justify-center my-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}

      {results && results.length > 0 && !isLoading && (
        <Card>
          <CardHeader>
            <CardTitle>Query Results</CardTitle>
            <CardDescription>
              Found {results.length} result{results.length !== 1 ? 's' : ''} for "{selectedQueryInfo?.name}"
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <div className="max-h-[500px] overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {Object.keys(results[0]).map((key) => (
                        <TableHead key={key} className="capitalize">
                          {key.replace(/_/g, ' ')}
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {results.map((row, i) => (
                      <TableRow key={i}>
                        {Object.entries(row).map(([key, value]) => (
                          <TableCell key={key}>
                            {value === null || value === undefined ? (
                              <span className="text-muted-foreground">N/A</span>
                            ) : typeof value === 'object' ? (
                              JSON.stringify(value)
                            ) : (
                              String(value)
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {results && results.length === 0 && !isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <p className="text-muted-foreground">No results found for this query</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
