
import { Search, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";

interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  paymentStatusFilter: string[];
  onPaymentStatusFilterChange: (statuses: string[]) => void;
  coursesFilter: string[];
  onCoursesFilterChange: (courses: string[]) => void;
  availableCourses: string[];
}

export function SearchBar({ 
  searchQuery, 
  onSearchChange,
  paymentStatusFilter,
  onPaymentStatusFilterChange,
  coursesFilter,
  onCoursesFilterChange,
  availableCourses
}: SearchBarProps) {
  const paymentStatuses = ['paid', 'pending', 'overdue'];

  const handlePaymentStatusChange = (status: string, checked: boolean) => {
    if (checked) {
      onPaymentStatusFilterChange([...paymentStatusFilter, status]);
    } else {
      onPaymentStatusFilterChange(paymentStatusFilter.filter(s => s !== status));
    }
  };

  const handleCourseChange = (course: string, checked: boolean) => {
    if (checked) {
      onCoursesFilterChange([...coursesFilter, course]);
    } else {
      onCoursesFilterChange(coursesFilter.filter(c => c !== course));
    }
  };

  const clearAllFilters = () => {
    onPaymentStatusFilterChange([]);
    onCoursesFilterChange([]);
  };

  const hasActiveFilters = paymentStatusFilter.length > 0 || coursesFilter.length > 0;

  return (
    <div className="flex items-center gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search students..."
          className="pl-9"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="icon" className={hasActiveFilters ? "bg-primary text-primary-foreground" : ""}>
            <Filter className="h-4 w-4" />
            <span className="sr-only">Filter</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <div className="px-2 py-1.5 text-sm font-medium">Payment Status</div>
          {paymentStatuses.map((status) => (
            <DropdownMenuCheckboxItem
              key={status}
              checked={paymentStatusFilter.includes(status)}
              onCheckedChange={(checked) => handlePaymentStatusChange(status, checked)}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </DropdownMenuCheckboxItem>
          ))}
          
          <DropdownMenuSeparator />
          
          <div className="px-2 py-1.5 text-sm font-medium">Courses</div>
          {availableCourses.map((course) => (
            <DropdownMenuCheckboxItem
              key={course}
              checked={coursesFilter.includes(course)}
              onCheckedChange={(checked) => handleCourseChange(course, checked)}
            >
              {course}
            </DropdownMenuCheckboxItem>
          ))}
          
          {hasActiveFilters && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={clearAllFilters}>
                Clear all filters
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
