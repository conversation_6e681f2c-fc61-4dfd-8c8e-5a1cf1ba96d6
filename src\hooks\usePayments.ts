// Re-export everything for backward compatibility
export type { Payment, PaymentMethod, PaymentStatus, PaymentFilters, CreatePaymentData, UpdatePaymentData } from "@/types/payment";
export { validatePaymentMethod, validatePaymentStatus, transformPayment } from "@/utils/paymentValidation";
export { usePaymentsCore as usePayments } from "@/hooks/usePaymentsCore";

// Keep the main export as usePayments for existing imports
export { usePaymentsCore } from "@/hooks/usePaymentsCore";
