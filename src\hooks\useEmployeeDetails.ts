
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

// Define Employee type directly here instead of importing from useTeachers
export type Employee = {
  id: string;
  name: string;
  email: string;
  department_id: string | null;
  role: string;
  salary: number | null;
  department?: {
    name: string;
  } | null;
  teaches?: any[];
  created_at: string;
};

export const useEmployeeDetails = (employeeId?: string) => {
  const queryClient = useQueryClient();

  const { data: employeeDetails, isLoading: loading, error } = useQuery({
    queryKey: ["employee", employeeId],
    queryFn: async () => {
      if (!employeeId) return null;

      console.log("Fetching employee details for ID:", employeeId);
      const { data, error } = await supabase
        .from("employees")
        .select(`
          *,
          department:departments!employees_department_id_fkey(name),
          teaches(
            course:courses(*)
          )
        `)
        .eq("id", employeeId)
        .maybeSingle();

      if (error) {
        console.error("Error fetching employee details:", error);
        throw error;
      }
      
      console.log("Employee details data:", data);
      return data;
    },
    enabled: !!employeeId
  });

  const updateEmployee = async (employeeId: string, updates: Partial<Employee>) => {
    const { error } = await supabase
      .from("employees")
      .update(updates)
      .eq("id", employeeId);

    if (error) throw error;
    return true;
  };

  const { mutateAsync: updateEmployeeMutation } = useMutation({
    mutationFn: ({ employeeId, updates }: { employeeId: string; updates: Partial<Employee> }) =>
      updateEmployee(employeeId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["employee", employeeId] });
      toast({
        title: "Success",
        description: "Employee details updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update employee details",
        variant: "destructive",
      });
      console.error("Error updating employee:", error);
    },
  });

  return {
    employeeDetails,
    loading,
    error,
    updateEmployee: updateEmployeeMutation
  };
};
